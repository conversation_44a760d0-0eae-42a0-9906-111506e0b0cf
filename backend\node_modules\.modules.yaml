hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.34.0(jiti@2.5.1))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.1':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.2':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.34.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.5':
    '@eslint/plugin-kit': private
  '@fastify/accept-negotiator@2.0.1':
    '@fastify/accept-negotiator': private
  '@fastify/ajv-compiler@4.0.2':
    '@fastify/ajv-compiler': private
  '@fastify/error@4.2.0':
    '@fastify/error': private
  '@fastify/fast-json-stringify-compiler@5.0.3':
    '@fastify/fast-json-stringify-compiler': private
  '@fastify/forwarded@3.0.0':
    '@fastify/forwarded': private
  '@fastify/merge-json-schemas@0.2.1':
    '@fastify/merge-json-schemas': private
  '@fastify/proxy-addr@5.0.0':
    '@fastify/proxy-addr': private
  '@fastify/send@4.1.0':
    '@fastify/send': private
  '@fastify/static@8.2.0':
    '@fastify/static': private
  '@fastify/websocket@11.2.0':
    '@fastify/websocket': private
  '@graphql-typed-document-node/core@3.2.0(graphql@16.11.0)':
    '@graphql-typed-document-node/core': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/balanced-match@4.0.1':
    '@isaacs/balanced-match': private
  '@isaacs/brace-expansion@5.0.0':
    '@isaacs/brace-expansion': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@lukeed/ms@2.0.2':
    '@lukeed/ms': private
  '@mapbox/node-pre-gyp@1.0.11':
    '@mapbox/node-pre-gyp': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@prisma/config@6.15.0':
    '@prisma/config': private
  '@prisma/debug@6.15.0':
    '@prisma/debug': private
  '@prisma/engines-version@6.15.0-5.85179d7826409ee107a6ba334b5e305ae3fba9fb':
    '@prisma/engines-version': private
  '@prisma/engines@6.15.0':
    '@prisma/engines': private
  '@prisma/fetch-engine@6.15.0':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@6.15.0':
    '@prisma/get-platform': private
  '@rollup/rollup-win32-x64-msvc@4.49.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@typescript-eslint/project-service@8.41.0(typescript@5.9.2)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.41.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.41.0(typescript@5.9.2)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.41.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.41.0(typescript@5.9.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.41.0(eslint@9.34.0(jiti@2.5.1))(typescript@5.9.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.41.0':
    '@typescript-eslint/visitor-keys': private
  '@vitest/expect@2.1.9':
    '@vitest/expect': private
  '@vitest/mocker@2.1.9(vite@5.4.19(@types/node@22.18.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@2.1.9':
    '@vitest/pretty-format': private
  '@vitest/runner@2.1.9':
    '@vitest/runner': private
  '@vitest/snapshot@2.1.9':
    '@vitest/snapshot': private
  '@vitest/spy@2.1.9':
    '@vitest/spy': private
  '@vitest/utils@2.1.9':
    '@vitest/utils': private
  abbrev@1.1.1:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  abstract-logging@2.0.1:
    abstract-logging: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  agent-base@6.0.2:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-formats@3.0.1(ajv@8.17.1):
    ajv-formats: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  aproba@2.1.0:
    aproba: private
  are-we-there-yet@2.0.0:
    are-we-there-yet: private
  argparse@2.0.1:
    argparse: private
  asn1.js@5.4.1:
    asn1.js: private
  assertion-error@2.0.1:
    assertion-error: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  avvio@9.1.0:
    avvio: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  bn.js@4.12.2:
    bn.js: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer@6.0.3:
    buffer: private
  c12@3.1.0:
    c12: private
  cac@6.7.14:
    cac: private
  callsites@3.1.0:
    callsites: private
  chai@5.3.3:
    chai: private
  chalk@4.1.2:
    chalk: private
  check-error@2.1.1:
    check-error: private
  chokidar@4.0.3:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  citty@0.1.6:
    citty: private
  clean-stack@2.2.0:
    clean-stack: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-support@1.1.3:
    color-support: private
  colorette@2.0.20:
    colorette: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.2.2:
    confbox: private
  consola@3.4.2:
    consola: private
  console-control-strings@1.1.0:
    console-control-strings: private
  content-disposition@0.5.4:
    content-disposition: private
  cookie@1.0.2:
    cookie: private
  cross-spawn@7.0.6:
    cross-spawn: private
  dateformat@4.6.3:
    dateformat: private
  debug@4.4.1:
    debug: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge-ts@7.1.5:
    deepmerge-ts: private
  defu@6.1.4:
    defu: private
  delegates@1.0.0:
    delegates: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destr@2.0.5:
    destr: private
  detect-libc@2.0.4:
    detect-libc: private
  dotenv@16.6.1:
    dotenv: private
  duplexify@4.1.3:
    duplexify: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  effect@3.16.12:
    effect: private
  emoji-regex@8.0.0:
    emoji-regex: private
  empathic@2.0.0:
    empathic: private
  end-of-stream@1.4.5:
    end-of-stream: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  esbuild@0.25.9:
    esbuild: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  event-target-shim@5.0.1:
    event-target-shim: private
  events@3.3.0:
    events: private
  expect-type@1.2.2:
    expect-type: private
  exsolve@1.0.7:
    exsolve: private
  fast-check@3.23.2:
    fast-check: private
  fast-copy@3.0.2:
    fast-copy: private
  fast-decode-uri-component@1.0.1:
    fast-decode-uri-component: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-json-stringify@6.0.1:
    fast-json-stringify: private
  fast-jwt@5.0.6:
    fast-jwt: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-querystring@1.1.2:
    fast-querystring: private
  fast-redact@3.5.0:
    fast-redact: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.1.0:
    fast-uri: private
  fastfall@1.5.1:
    fastfall: private
  fastify-plugin@5.0.1:
    fastify-plugin: private
  fastparallel@2.4.1:
    fastparallel: private
  fastq@1.19.1:
    fastq: private
  fastseries@1.7.2:
    fastseries: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-my-way@9.3.0:
    find-my-way: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  foreground-child@3.3.1:
    foreground-child: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  gauge@3.0.2:
    gauge: private
  generate-function@2.3.1:
    generate-function: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  giget@2.0.0:
    giget: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@11.0.3:
    glob: private
  globals@14.0.0:
    globals: private
  graphemer@1.4.0:
    graphemer: private
  graphql-jit@0.8.6(graphql@16.11.0):
    graphql-jit: private
  has-flag@4.0.0:
    has-flag: private
  has-unicode@2.0.1:
    has-unicode: private
  helmet@7.2.0:
    helmet: private
  help-me@5.0.0:
    help-me: private
  http-errors@2.0.0:
    http-errors: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  ieee754@1.2.1:
    ieee754: private
  ignore@7.0.5:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@2.2.0:
    ipaddr.js: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-property@1.0.2:
    is-property: private
  isexe@2.0.0:
    isexe: private
  jackspeak@4.1.1:
    jackspeak: private
  jiti@2.5.1:
    jiti: private
  joycon@3.1.1:
    joycon: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-ref-resolver@2.0.1:
    json-schema-ref-resolver: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  light-my-request@6.6.0:
    light-my-request: private
  locate-path@6.0.0:
    locate-path: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.mergewith@4.6.2:
    lodash.mergewith: private
  lodash.once@4.1.1:
    lodash.once: private
  loupe@3.2.1:
    loupe: private
  lru-cache@11.1.0:
    lru-cache: private
  magic-string@0.30.18:
    magic-string: private
  make-dir@3.1.0:
    make-dir: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime@3.0.0:
    mime: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp@1.0.4:
    mkdirp: private
  mnemonist@0.40.0:
    mnemonist: private
  mqemitter@6.0.2:
    mqemitter: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  node-addon-api@5.1.0:
    node-addon-api: private
  node-fetch-native@1.6.7:
    node-fetch-native: private
  node-fetch@2.7.0:
    node-fetch: private
  nopt@5.0.0:
    nopt: private
  npmlog@5.0.1:
    npmlog: private
  nypm@0.6.1:
    nypm: private
  object-assign@4.1.1:
    object-assign: private
  obliterator@2.0.5:
    obliterator: private
  ohash@2.0.11:
    ohash: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  once@1.4.0:
    once: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-scurry@2.0.0:
    path-scurry: private
  pathe@1.1.2:
    pathe: private
  pathval@2.0.1:
    pathval: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pino-abstract-transport@2.0.0:
    pino-abstract-transport: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pkg-types@2.3.0:
    pkg-types: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  process-warning@5.0.0:
    process-warning: private
  process@0.11.10:
    process: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  qlobber@8.0.1:
    qlobber: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  quick-lru@7.1.0:
    quick-lru: private
  rc9@2.1.2:
    rc9: private
  readable-stream@4.7.0:
    readable-stream: private
  readdirp@4.1.2:
    readdirp: private
  real-require@0.2.0:
    real-require: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  ret@0.5.0:
    ret: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.49.0:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex2@5.0.0:
    safe-regex2: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  secure-json-parse@4.0.0:
    secure-json-parse: private
  semver@7.7.2:
    semver: private
  set-blocking@2.0.0:
    set-blocking: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  single-user-cache@1.0.1:
    single-user-cache: private
  sonic-boom@4.2.0:
    sonic-boom: private
  source-map-js@1.2.1:
    source-map-js: private
  split2@4.2.0:
    split2: private
  stackback@0.0.2:
    stackback: private
  statuses@2.0.1:
    statuses: private
  std-env@3.9.0:
    std-env: private
  steed@1.1.3:
    steed: private
  stream-shift@1.0.3:
    stream-shift: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  tar@6.2.1:
    tar: private
  thread-stream@3.1.0:
    thread-stream: private
  tiny-lru@11.4.5:
    tiny-lru: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinypool@1.1.1:
    tinypool: private
  tinyrainbow@1.2.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toad-cache@3.7.0:
    toad-cache: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  ts-api-utils@2.1.0(typescript@5.9.2):
    ts-api-utils: private
  type-check@0.4.0:
    type-check: private
  undici-types@6.21.0:
    undici-types: private
  undici@6.21.3:
    undici: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vite-node@2.1.9(@types/node@22.18.0):
    vite-node: private
  vite@5.4.19(@types/node@22.18.0):
    vite: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  wide-align@1.1.5:
    wide-align: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.3:
    ws: private
  xtend@4.0.2:
    xtend: private
  yallist@4.0.0:
    yallist: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
  - bcrypt
  - '@prisma/engines'
  - prisma
  - '@prisma/client'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Sat, 30 Aug 2025 13:50:09 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.9'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.9'
  - '@rollup/rollup-android-arm-eabi@4.49.0'
  - '@rollup/rollup-android-arm64@4.49.0'
  - '@rollup/rollup-darwin-arm64@4.49.0'
  - '@rollup/rollup-darwin-x64@4.49.0'
  - '@rollup/rollup-freebsd-arm64@4.49.0'
  - '@rollup/rollup-freebsd-x64@4.49.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.49.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.49.0'
  - '@rollup/rollup-linux-arm64-gnu@4.49.0'
  - '@rollup/rollup-linux-arm64-musl@4.49.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.49.0'
  - '@rollup/rollup-linux-ppc64-gnu@4.49.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.49.0'
  - '@rollup/rollup-linux-riscv64-musl@4.49.0'
  - '@rollup/rollup-linux-s390x-gnu@4.49.0'
  - '@rollup/rollup-linux-x64-gnu@4.49.0'
  - '@rollup/rollup-linux-x64-musl@4.49.0'
  - '@rollup/rollup-win32-arm64-msvc@4.49.0'
  - '@rollup/rollup-win32-ia32-msvc@4.49.0'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
