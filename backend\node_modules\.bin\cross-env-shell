#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/bin/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/cross-env@7.0.3/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/bin/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/src/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/cross-env@7.0.3/node_modules/cross-env/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/cross-env@7.0.3/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/cross-env@7.0.3/node_modules/cross-env/src/bin/cross-env-shell.js" "$@"
else
  exec node  "$basedir/../.pnpm/cross-env@7.0.3/node_modules/cross-env/src/bin/cross-env-shell.js" "$@"
fi
