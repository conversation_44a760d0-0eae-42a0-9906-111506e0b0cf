{"name": "@tailwindcss/oxide-wasm32-wasi", "version": "4.1.12", "cpu": ["wasm32"], "main": "tailwindcss-oxide.wasi.cjs", "files": ["tailwindcss-oxide.wasm32-wasi.wasm", "tailwindcss-oxide.wasi.cjs", "tailwindcss-oxide.wasi-browser.js", "wasi-worker.mjs", "wasi-worker-browser.mjs"], "license": "MIT", "engines": {"node": ">=14.0.0"}, "publishConfig": {"provenance": true, "access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/tailwindlabs/tailwindcss.git", "directory": "crates/node"}, "browser": "tailwindcss-oxide.wasi-browser.js", "dependencies": {"@napi-rs/wasm-runtime": "^0.2.12", "@emnapi/core": "^1.4.5", "@emnapi/runtime": "^1.4.5", "@tybys/wasm-util": "^0.10.0", "@emnapi/wasi-threads": "^1.0.4", "tslib": "^2.8.0"}, "bundledDependencies": ["@napi-rs/wasm-runtime", "@emnapi/core", "@emnapi/runtime", "@tybys/wasm-util", "@emnapi/wasi-threads", "tslib"]}