{"version": 3, "file": "upstreamPullRequest.js", "names": ["_Function", "require", "_Predicate", "OpCodes", "_interopRequireWildcard", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "UpstreamPullRequestSymbolKey", "UpstreamPullRequestTypeId", "exports", "Symbol", "for", "upstreamPullRequestVariance", "_A", "_", "proto", "Pulled", "value", "op", "create", "_tag", "OP_PULLED", "NoUpstream", "activeDownstreamCount", "OP_NO_UPSTREAM", "isUpstreamPullRequest", "u", "hasProperty", "isPulled", "self", "isNoUpstream", "match", "dual", "onNoUpstream", "onPulled"], "sources": ["../../../../src/internal/channel/upstreamPullRequest.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAC,uBAAA,CAAAH,OAAA;AAAmE,SAAAG,wBAAAC,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAH,uBAAA,YAAAA,CAAAC,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEnE;AACA,MAAMkB,4BAA4B,GAAG,mCAAmC;AAExE;AACO,MAAMC,yBAAyB,GAAAC,OAAA,CAAAD,yBAAA,gBAAkDE,MAAM,CAACC,GAAG,CAChGJ,4BAA4B,CACoB;AAElD,MAAMK,2BAA2B,GAAG;EAClC;EACAC,EAAE,EAAGC,CAAQ,IAAKA;CACnB;AAED;AACA,MAAMC,KAAK,GAAG;EACZ,CAACP,yBAAyB,GAAGI;CAC9B;AAED;AACO,MAAMI,MAAM,GAAOC,KAAQ,IAAgD;EAChF,MAAMC,EAAE,GAAGd,MAAM,CAACe,MAAM,CAACJ,KAAK,CAAC;EAC/BG,EAAE,CAACE,IAAI,GAAGlC,OAAO,CAACmC,SAAS;EAC3BH,EAAE,CAACD,KAAK,GAAGA,KAAK;EAChB,OAAOC,EAAE;AACX,CAAC;AAED;AAAAT,OAAA,CAAAO,MAAA,GAAAA,MAAA;AACO,MAAMM,UAAU,GAAIC,qBAA6B,IAAoD;EAC1G,MAAML,EAAE,GAAGd,MAAM,CAACe,MAAM,CAACJ,KAAK,CAAC;EAC/BG,EAAE,CAACE,IAAI,GAAGlC,OAAO,CAACsC,cAAc;EAChCN,EAAE,CAACK,qBAAqB,GAAGA,qBAAqB;EAChD,OAAOL,EAAE;AACX,CAAC;AAED;AAAAT,OAAA,CAAAa,UAAA,GAAAA,UAAA;AACO,MAAMG,qBAAqB,GAAIC,CAAU,IAC9C,IAAAC,sBAAW,EAACD,CAAC,EAAElB,yBAAyB,CAAC;AAE3C;AAAAC,OAAA,CAAAgB,qBAAA,GAAAA,qBAAA;AACO,MAAMG,QAAQ,GACnBC,IAAgD,IACNA,IAAI,CAACT,IAAI,KAAKlC,OAAO,CAACmC,SAAS;AAE3E;AAAAZ,OAAA,CAAAmB,QAAA,GAAAA,QAAA;AACO,MAAME,YAAY,GACvBD,IAAgD,IACLA,IAAI,CAACT,IAAI,KAAKlC,OAAO,CAACsC,cAAc;AAEjF;AAAAf,OAAA,CAAAqB,YAAA,GAAAA,YAAA;AACO,MAAMC,KAAK,GAAAtB,OAAA,CAAAsB,KAAA,gBAAG,IAAAC,cAAI,EAcvB,CAAC,EAAE,CACHH,IAAgD,EAChD;EAAEI,YAAY;EAAEC;AAAQ,CAGvB,KACI;EACL,QAAQL,IAAI,CAACT,IAAI;IACf,KAAKlC,OAAO,CAACmC,SAAS;MAAE;QACtB,OAAOa,QAAQ,CAACL,IAAI,CAACZ,KAAK,CAAC;MAC7B;IACA,KAAK/B,OAAO,CAACsC,cAAc;MAAE;QAC3B,OAAOS,YAAY,CAACJ,IAAI,CAACN,qBAAqB,CAAC;MACjD;EACF;AACF,CAAC,CAAC", "ignoreList": []}