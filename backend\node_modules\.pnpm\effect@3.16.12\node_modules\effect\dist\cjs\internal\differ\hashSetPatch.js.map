{"version": 3, "file": "hashSetPatch.js", "names": ["Chunk", "_interopRequireWildcard", "require", "Dual", "HashSet", "_data", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "HashSetPatchTypeId", "exports", "Symbol", "for", "variance", "a", "PatchProto", "Structural", "prototype", "_Value", "_Key", "_Patch", "EmptyProto", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "AddProto", "makeAdd", "value", "RemoveProto", "makeRemove", "diff", "oldValue", "newValue", "removed", "patch", "reduce", "remove", "combine", "dual", "self", "that", "patches", "of", "isNonEmpty", "head", "headNonEmpty", "tail", "tailNonEmpty", "prepend", "add"], "sources": ["../../../../src/internal/differ/hashSetPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,IAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AAAuC,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEvC;AACO,MAAMkB,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,gBAA0BE,MAAM,CAACC,GAAG,CACjE,2BAA2B,CACH;AAE1B,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA;AACA,MAAMC,UAAU,GAAG;EACjB,GAAGC,gBAAU,CAACC,SAAS;EACvB,CAACR,kBAAkB,GAAG;IACpBS,MAAM,EAAEL,QAAQ;IAChBM,IAAI,EAAEN,QAAQ;IACdO,MAAM,EAAEP;;CAEX;AAMD,MAAMQ,UAAU,gBAAGf,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC1DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGnB,MAAM,CAACiB,MAAM,CAACF,UAAU,CAAC;AAExC;AACO,MAAMK,KAAK,GAAGA,CAAA,KAA0CD,MAAM;AAAAf,OAAA,CAAAgB,KAAA,GAAAA,KAAA;AAQrE,MAAMC,YAAY,gBAAGrB,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMI,WAAW,GAAGA,CACzBC,KAAkC,EAClCC,MAAmC,KACJ;EAC/B,MAAMlC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACI,YAAY,CAAC;EACrC/B,CAAC,CAACiC,KAAK,GAAGA,KAAK;EACfjC,CAAC,CAACkC,MAAM,GAAGA,MAAM;EACjB,OAAOlC,CAAC;AACV,CAAC;AAAAc,OAAA,CAAAkB,WAAA,GAAAA,WAAA;AAOD,MAAMG,QAAQ,gBAAGzB,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EACxDS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMQ,OAAO,GAClBC,KAAY,IACmB;EAC/B,MAAMrC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACQ,QAAQ,CAAC;EACjCnC,CAAC,CAACqC,KAAK,GAAGA,KAAK;EACf,OAAOrC,CAAC;AACV,CAAC;AAAAc,OAAA,CAAAsB,OAAA,GAAAA,OAAA;AAOD,MAAME,WAAW,gBAAG5B,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC3DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMW,UAAU,GACrBF,KAAY,IACmB;EAC/B,MAAMrC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACW,WAAW,CAAC;EACpCtC,CAAC,CAACqC,KAAK,GAAGA,KAAK;EACf,OAAOrC,CAAC;AACV,CAAC;AAQD;AAAAc,OAAA,CAAAyB,UAAA,GAAAA,UAAA;AACO,MAAMC,IAAI,GAAGA,CAClBC,QAAgC,EAChCC,QAAgC,KACD;EAC/B,MAAM,CAACC,OAAO,EAAEC,KAAK,CAAC,GAAGpD,OAAO,CAACqD,MAAM,CACrC,CAACJ,QAAQ,EAAEX,KAAK,EAAS,CAAU,EACnC,CAAC,CAACvB,GAAG,EAAEqC,KAAK,CAAC,EAAEP,KAAY,KAAI;IAC7B,IAAI7C,OAAO,CAACa,GAAG,CAACgC,KAAK,CAAC,CAAC9B,GAAG,CAAC,EAAE;MAC3B,OAAO,CAACf,OAAO,CAACsD,MAAM,CAACT,KAAK,CAAC,CAAC9B,GAAG,CAAC,EAAEqC,KAAK,CAAU;IACrD;IACA,OAAO,CAACrC,GAAG,EAAEwC,OAAO,CAACX,OAAO,CAACC,KAAK,CAAC,CAAC,CAACO,KAAK,CAAC,CAAU;EACvD,CAAC,CACF,CAACF,QAAQ,CAAC;EACX,OAAOlD,OAAO,CAACqD,MAAM,CAACD,KAAK,EAAE,CAACA,KAAK,EAAEP,KAAY,KAAKU,OAAO,CAACR,UAAU,CAACF,KAAK,CAAC,CAAC,CAACO,KAAK,CAAC,CAAC,CAACD,OAAO,CAAC;AACnG,CAAC;AAED;AAAA7B,OAAA,CAAA0B,IAAA,GAAAA,IAAA;AACO,MAAMO,OAAO,GAAAjC,OAAA,CAAAiC,OAAA,gBAAGxD,IAAI,CAACyD,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAKlB,WAAW,CAACiB,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACO,MAAMN,KAAK,GAAA9B,OAAA,CAAA8B,KAAA,gBAAGrD,IAAI,CAACyD,IAAI,CAU5B,CAAC,EAAE,CACHC,IAAiC,EACjCR,QAAgC,KAC9B;EACF,IAAKQ,IAAoB,CAACrB,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOa,QAAQ;EACjB;EACA,IAAIlC,GAAG,GAAGkC,QAAQ;EAClB,IAAIU,OAAO,GAA6C/D,KAAK,CAACgE,EAAE,CAACH,IAAI,CAAC;EACtE,OAAO7D,KAAK,CAACiE,UAAU,CAACF,OAAO,CAAC,EAAE;IAChC,MAAMG,IAAI,GAAgBlE,KAAK,CAACmE,YAAY,CAACJ,OAAO,CAAgB;IACpE,MAAMK,IAAI,GAAGpE,KAAK,CAACqE,YAAY,CAACN,OAAO,CAAC;IACxC,QAAQG,IAAI,CAAC1B,IAAI;MACf,KAAK,OAAO;QAAE;UACZuB,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdL,OAAO,GAAG/D,KAAK,CAACsE,OAAO,CAACJ,IAAI,CAACrB,KAAK,CAAC,CAAC7C,KAAK,CAACsE,OAAO,CAACJ,IAAI,CAACpB,MAAM,CAAC,CAACsB,IAAI,CAAC,CAAC;UACrE;QACF;MACA,KAAK,KAAK;QAAE;UACVjD,GAAG,GAAGf,OAAO,CAACmE,GAAG,CAACL,IAAI,CAACjB,KAAK,CAAC,CAAC9B,GAAG,CAAC;UAClC4C,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACbjD,GAAG,GAAGf,OAAO,CAACsD,MAAM,CAACQ,IAAI,CAACjB,KAAK,CAAC,CAAC9B,GAAG,CAAC;UACrC4C,OAAO,GAAGK,IAAI;QAChB;IACF;EACF;EACA,OAAOjD,GAAG;AACZ,CAAC,CAAC", "ignoreList": []}