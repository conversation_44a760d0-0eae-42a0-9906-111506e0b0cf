"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateMergedSchema = generateMergedSchema;
exports.getModuleSchemas = getModuleSchemas;
const fs_1 = require("fs");
const path_1 = require("path");
function getModuleSchemas() {
    const modulesDir = (0, path_1.join)(__dirname, '../modules');
    if (!(0, fs_1.existsSync)(modulesDir)) {
        console.log('No modules directory found, creating empty schema...');
        return [];
    }
    const modules = (0, fs_1.readdirSync)(modulesDir, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
    const schemas = [];
    for (const moduleName of modules) {
        const schemaPath = (0, path_1.join)(modulesDir, moduleName, 'prisma', 'schema.prisma');
        if ((0, fs_1.existsSync)(schemaPath)) {
            let content = (0, fs_1.readFileSync)(schemaPath, 'utf-8');
            content = content
                .replace(/generator\s+\w+\s*\{[^}]*\}/g, '')
                .replace(/datasource\s+\w+\s*\{[^}]*\}/g, '')
                .trim();
            if (content.trim()) {
                schemas.push({ name: moduleName, content });
            }
        }
    }
    return schemas;
}
function generateMergedSchema() {
    const schemas = getModuleSchemas();
    const header = `// This file is auto-generated by scripts/merge-schemas.ts
// Do not edit this file directly. Edit individual module schemas instead.

generator client {
  provider = "prisma-client-js"
  output   = "./generated"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

`;
    const mergedContent = schemas
        .map(schema => `// === ${schema.name.toUpperCase()} MODULE ===\n${schema.content}`)
        .join('\n\n');
    return header + mergedContent;
}
function main() {
    try {
        const mergedSchema = generateMergedSchema();
        const outputPath = (0, path_1.join)(__dirname, '../prisma/schema.prisma');
        (0, fs_1.writeFileSync)(outputPath, mergedSchema);
        console.log('✅ Successfully merged schemas from all modules');
        console.log(`📄 Output: ${outputPath}`);
        const schemas = getModuleSchemas();
        if (schemas.length > 0) {
            console.log(`🔧 Merged ${schemas.length} module(s): ${schemas.map(s => s.name).join(', ')}`);
        }
        else {
            console.log('📝 Created empty schema (no modules found)');
        }
    }
    catch (error) {
        console.error('❌ Error merging schemas:', error);
        process.exit(1);
    }
}
if (require.main === module) {
    main();
}
//# sourceMappingURL=merge-schemas.js.map