"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const app_1 = require("./app");
const logger_1 = require("./shared/kernel/logger");
async function start() {
    try {
        const app = await (0, app_1.createApp)();
        const port = parseInt(process.env.PORT || '4000');
        const host = process.env.HOST || '0.0.0.0';
        await app.listen({ port, host });
        logger_1.logger.info(`🚀 Server ready at http://${host}:${port}`);
        logger_1.logger.info(`📊 GraphQL Playground available at http://${host}:${port}/graphiql`);
        logger_1.logger.info(`🏥 Health check available at http://${host}:${port}/health`);
    }
    catch (error) {
        logger_1.logger.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}
process.on('SIGINT', () => {
    logger_1.logger.info('🛑 Received SIGINT, shutting down gracefully...');
    process.exit(0);
});
process.on('SIGTERM', () => {
    logger_1.logger.info('🛑 Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});
process.on('unhandledRejection', (reason, promise) => {
    logger_1.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    logger_1.logger.error('Uncaught Exception:', error);
    process.exit(1);
});
start().catch((error) => {
    logger_1.logger.error('Failed to start application:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map