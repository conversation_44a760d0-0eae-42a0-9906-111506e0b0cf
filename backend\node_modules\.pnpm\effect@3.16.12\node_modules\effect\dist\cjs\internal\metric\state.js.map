{"version": 3, "file": "state.js", "names": ["Arr", "_interopRequireWildcard", "require", "Equal", "_Function", "Hash", "_Pipeable", "_Predicate", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "MetricStateSymbolKey", "MetricStateTypeId", "exports", "Symbol", "for", "CounterStateSymbolKey", "CounterStateTypeId", "FrequencyStateSymbolKey", "FrequencyStateTypeId", "GaugeStateSymbolKey", "GaugeStateTypeId", "HistogramStateSymbolKey", "HistogramStateTypeId", "SummaryStateSymbolKey", "SummaryStateTypeId", "metricStateVariance", "_A", "_", "CounterState", "count", "constructor", "symbol", "pipe", "hash", "combine", "cached", "that", "isCounterState", "pipeArguments", "arguments", "arrayEquals", "getEquivalence", "equals", "FrequencyState", "occurrences", "_hash", "string", "array", "fromIterable", "entries", "isFrequencyState", "GaugeState", "value", "u", "isGaugeState", "HistogramState", "buckets", "min", "max", "sum", "isHistogramState", "SummaryState", "error", "quantiles", "isSummaryState", "counter", "frequency", "gauge", "histogram", "options", "summary", "isMetricState", "hasProperty"], "sources": ["../../../../src/internal/metric/state.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAGA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,UAAA,GAAAL,OAAA;AAAgD,SAAAD,wBAAAO,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAO,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEhD;AACA,MAAMkB,oBAAoB,GAAG,oBAAoB;AAEjD;AACO,MAAMC,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,gBAAkCE,MAAM,CAACC,GAAG,CACxEJ,oBAAoB,CACY;AAElC;AACA,MAAMK,qBAAqB,GAAG,4BAA4B;AAE1D;AACO,MAAMC,kBAAkB,GAAAJ,OAAA,CAAAI,kBAAA,gBAAmCH,MAAM,CAACC,GAAG,CAC1EC,qBAAqB,CACY;AAEnC;AACA,MAAME,uBAAuB,GAAG,8BAA8B;AAE9D;AACO,MAAMC,oBAAoB,GAAAN,OAAA,CAAAM,oBAAA,gBAAqCL,MAAM,CAACC,GAAG,CAC9EG,uBAAuB,CACY;AAErC;AACA,MAAME,mBAAmB,GAAG,0BAA0B;AAEtD;AACO,MAAMC,gBAAgB,GAAAR,OAAA,CAAAQ,gBAAA,gBAAiCP,MAAM,CAACC,GAAG,CACtEK,mBAAmB,CACY;AAEjC;AACA,MAAME,uBAAuB,GAAG,8BAA8B;AAE9D;AACO,MAAMC,oBAAoB,GAAAV,OAAA,CAAAU,oBAAA,gBAAqCT,MAAM,CAACC,GAAG,CAC9EO,uBAAuB,CACY;AAErC;AACA,MAAME,qBAAqB,GAAG,4BAA4B;AAE1D;AACO,MAAMC,kBAAkB,GAAAZ,OAAA,CAAAY,kBAAA,gBAAmCX,MAAM,CAACC,GAAG,CAC1ES,qBAAqB,CACY;AAEnC,MAAME,mBAAmB,GAAG;EAC1B;EACAC,EAAE,EAAGC,CAAU,IAAKA;CACrB;AAED;AACA,MAAMC,YAAY;EAGKC,KAAA;EAFZ,CAAClB,iBAAiB,IAAIc,mBAAmB;EACzC,CAACT,kBAAkB,IAAoCA,kBAAkB;EAClFc,YAAqBD,KAAQ;IAAR,KAAAA,KAAK,GAALA,KAAK;EAAM;EAChC,CAACzC,IAAI,CAAC2C,MAAM,IAAC;IACX,OAAO,IAAAC,cAAI,EACT5C,IAAI,CAAC6C,IAAI,CAAClB,qBAAqB,CAAC,EAChC3B,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACJ,KAAK,CAAC,CAAC,EACnCzC,IAAI,CAAC+C,MAAM,CAAC,IAAI,CAAC,CAClB;EACH;EACA,CAACjD,KAAK,CAAC6C,MAAM,EAAEK,IAAa;IAC1B,OAAOC,cAAc,CAACD,IAAI,CAAC,IAAI,IAAI,CAACP,KAAK,KAAKO,IAAI,CAACP,KAAK;EAC1D;EACAG,IAAIA,CAAA;IACF,OAAO,IAAAM,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF,MAAMC,WAAW,gBAAGzD,GAAG,CAAC0D,cAAc,CAACvD,KAAK,CAACwD,MAAM,CAAC;AAEpD;AACA,MAAMC,cAAc;EAGGC,WAAA;EAFZ,CAACjC,iBAAiB,IAAIc,mBAAmB;EACzC,CAACP,oBAAoB,IAAsCA,oBAAoB;EACxFY,YAAqBc,WAAwC;IAAxC,KAAAA,WAAW,GAAXA,WAAW;EAAgC;EAChEC,KAAK;EACL,CAACzD,IAAI,CAAC2C,MAAM,IAAC;IACX,OAAO,IAAAC,cAAI,EACT5C,IAAI,CAAC0D,MAAM,CAAC7B,uBAAuB,CAAC,EACpC7B,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC2D,KAAK,CAAChE,GAAG,CAACiE,YAAY,CAAC,IAAI,CAACJ,WAAW,CAACK,OAAO,EAAE,CAAC,CAAC,CAAC,EACtE7D,IAAI,CAAC+C,MAAM,CAAC,IAAI,CAAC,CAClB;EACH;EACA,CAACjD,KAAK,CAAC6C,MAAM,EAAEK,IAAa;IAC1B,OAAOc,gBAAgB,CAACd,IAAI,CAAC,IAAII,WAAW,CAC1CzD,GAAG,CAACiE,YAAY,CAAC,IAAI,CAACJ,WAAW,CAACK,OAAO,EAAE,CAAC,EAC5ClE,GAAG,CAACiE,YAAY,CAACZ,IAAI,CAACQ,WAAW,CAACK,OAAO,EAAE,CAAC,CAC7C;EACH;EACAjB,IAAIA,CAAA;IACF,OAAO,IAAAM,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AACA,MAAMY,UAAU;EAGOC,KAAA;EAFZ,CAACzC,iBAAiB,IAAIc,mBAAmB;EACzC,CAACL,gBAAgB,IAAkCA,gBAAgB;EAC5EU,YAAqBsB,KAAQ;IAAR,KAAAA,KAAK,GAALA,KAAK;EAAM;EAChC,CAAChE,IAAI,CAAC2C,MAAM,IAAC;IACX,OAAO,IAAAC,cAAI,EACT5C,IAAI,CAAC6C,IAAI,CAACd,mBAAmB,CAAC,EAC9B/B,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACmB,KAAK,CAAC,CAAC,EACnChE,IAAI,CAAC+C,MAAM,CAAC,IAAI,CAAC,CAClB;EACH;EACA,CAACjD,KAAK,CAAC6C,MAAM,EAAEsB,CAAU;IACvB,OAAOC,YAAY,CAACD,CAAC,CAAC,IAAI,IAAI,CAACD,KAAK,KAAKC,CAAC,CAACD,KAAK;EAClD;EACApB,IAAIA,CAAA;IACF,OAAO,IAAAM,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AACM,MAAOgB,cAAc;EAIdC,OAAA;EACA3B,KAAA;EACA4B,GAAA;EACAC,GAAA;EACAC,GAAA;EAPF,CAAChD,iBAAiB,IAAIc,mBAAmB;EACzC,CAACH,oBAAoB,IAAsCA,oBAAoB;EACxFQ,YACW0B,OAAiD,EACjD3B,KAAa,EACb4B,GAAW,EACXC,GAAW,EACXC,GAAW;IAJX,KAAAH,OAAO,GAAPA,OAAO;IACP,KAAA3B,KAAK,GAALA,KAAK;IACL,KAAA4B,GAAG,GAAHA,GAAG;IACH,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,GAAG,GAAHA,GAAG;EACX;EACH,CAACvE,IAAI,CAAC2C,MAAM,IAAC;IACX,OAAO,IAAAC,cAAI,EACT5C,IAAI,CAAC6C,IAAI,CAACZ,uBAAuB,CAAC,EAClCjC,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACuB,OAAO,CAAC,CAAC,EACrCpE,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACJ,KAAK,CAAC,CAAC,EACnCzC,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACwB,GAAG,CAAC,CAAC,EACjCrE,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACyB,GAAG,CAAC,CAAC,EACjCtE,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAAC0B,GAAG,CAAC,CAAC,EACjCvE,IAAI,CAAC+C,MAAM,CAAC,IAAI,CAAC,CAClB;EACH;EACA,CAACjD,KAAK,CAAC6C,MAAM,EAAEK,IAAa;IAC1B,OAAOwB,gBAAgB,CAACxB,IAAI,CAAC,IAC3BlD,KAAK,CAACwD,MAAM,CAAC,IAAI,CAACc,OAAO,EAAEpB,IAAI,CAACoB,OAAO,CAAC,IACxC,IAAI,CAAC3B,KAAK,KAAKO,IAAI,CAACP,KAAK,IACzB,IAAI,CAAC4B,GAAG,KAAKrB,IAAI,CAACqB,GAAG,IACrB,IAAI,CAACC,GAAG,KAAKtB,IAAI,CAACsB,GAAG,IACrB,IAAI,CAACC,GAAG,KAAKvB,IAAI,CAACuB,GAAG;EACzB;EACA3B,IAAIA,CAAA;IACF,OAAO,IAAAM,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AAAA3B,OAAA,CAAA2C,cAAA,GAAAA,cAAA;AACM,MAAOM,YAAY;EAIZC,KAAA;EACAC,SAAA;EACAlC,KAAA;EACA4B,GAAA;EACAC,GAAA;EACAC,GAAA;EARF,CAAChD,iBAAiB,IAAIc,mBAAmB;EACzC,CAACD,kBAAkB,IAAoCA,kBAAkB;EAClFM,YACWgC,KAAa,EACbC,SAAkE,EAClElC,KAAa,EACb4B,GAAW,EACXC,GAAW,EACXC,GAAW;IALX,KAAAG,KAAK,GAALA,KAAK;IACL,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAlC,KAAK,GAALA,KAAK;IACL,KAAA4B,GAAG,GAAHA,GAAG;IACH,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,GAAG,GAAHA,GAAG;EACX;EACH,CAACvE,IAAI,CAAC2C,MAAM,IAAC;IACX,OAAO,IAAAC,cAAI,EACT5C,IAAI,CAAC6C,IAAI,CAACV,qBAAqB,CAAC,EAChCnC,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAAC6B,KAAK,CAAC,CAAC,EACnC1E,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAAC8B,SAAS,CAAC,CAAC,EACvC3E,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACJ,KAAK,CAAC,CAAC,EACnCzC,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACwB,GAAG,CAAC,CAAC,EACjCrE,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAACyB,GAAG,CAAC,CAAC,EACjCtE,IAAI,CAAC8C,OAAO,CAAC9C,IAAI,CAAC6C,IAAI,CAAC,IAAI,CAAC0B,GAAG,CAAC,CAAC,EACjCvE,IAAI,CAAC+C,MAAM,CAAC,IAAI,CAAC,CAClB;EACH;EACA,CAACjD,KAAK,CAAC6C,MAAM,EAAEK,IAAa;IAC1B,OAAO4B,cAAc,CAAC5B,IAAI,CAAC,IACzB,IAAI,CAAC0B,KAAK,KAAK1B,IAAI,CAAC0B,KAAK,IACzB5E,KAAK,CAACwD,MAAM,CAAC,IAAI,CAACqB,SAAS,EAAE3B,IAAI,CAAC2B,SAAS,CAAC,IAC5C,IAAI,CAAClC,KAAK,KAAKO,IAAI,CAACP,KAAK,IACzB,IAAI,CAAC4B,GAAG,KAAKrB,IAAI,CAACqB,GAAG,IACrB,IAAI,CAACC,GAAG,KAAKtB,IAAI,CAACsB,GAAG,IACrB,IAAI,CAACC,GAAG,KAAKvB,IAAI,CAACuB,GAAG;EACzB;EACA3B,IAAIA,CAAA;IACF,OAAO,IAAAM,uBAAa,EAAC,IAAI,EAAEC,SAAS,CAAC;EACvC;;AAGF;AAAA3B,OAAA,CAAAiD,YAAA,GAAAA,YAAA;AACO,MAAMI,OAAO,GAGfpC,KAAK,IAAK,IAAID,YAAY,CAACC,KAAK,CAAQ;AAE7C;AAAAjB,OAAA,CAAAqD,OAAA,GAAAA,OAAA;AACO,MAAMC,SAAS,GAAItB,WAAwC,IAAuC;EACvG,OAAO,IAAID,cAAc,CAACC,WAAW,CAAC;AACxC,CAAC;AAED;AAAAhC,OAAA,CAAAsD,SAAA,GAAAA,SAAA;AACO,MAAMC,KAAK,GAGbtC,KAAK,IAAK,IAAIsB,UAAU,CAACtB,KAAK,CAAQ;AAE3C;AAAAjB,OAAA,CAAAuD,KAAA,GAAAA,KAAA;AACO,MAAMC,SAAS,GACpBC,OAMC,IAED,IAAId,cAAc,CAChBc,OAAO,CAACb,OAAO,EACfa,OAAO,CAACxC,KAAK,EACbwC,OAAO,CAACZ,GAAG,EACXY,OAAO,CAACX,GAAG,EACXW,OAAO,CAACV,GAAG,CACZ;AAEH;AAAA/C,OAAA,CAAAwD,SAAA,GAAAA,SAAA;AACO,MAAME,OAAO,GAClBD,OAOC,IAED,IAAIR,YAAY,CACdQ,OAAO,CAACP,KAAK,EACbO,OAAO,CAACN,SAAS,EACjBM,OAAO,CAACxC,KAAK,EACbwC,OAAO,CAACZ,GAAG,EACXY,OAAO,CAACX,GAAG,EACXW,OAAO,CAACV,GAAG,CACZ;AAEH;AAAA/C,OAAA,CAAA0D,OAAA,GAAAA,OAAA;AACO,MAAMC,aAAa,GAAIlB,CAAU,IACtC,IAAAmB,sBAAW,EAACnB,CAAC,EAAE1C,iBAAiB,CAAC;AAEnC;AAAAC,OAAA,CAAA2D,aAAA,GAAAA,aAAA;AACO,MAAMlC,cAAc,GAAIgB,CAAU,IACvC,IAAAmB,sBAAW,EAACnB,CAAC,EAAErC,kBAAkB,CAAC;AAEpC;;;;AAAAJ,OAAA,CAAAyB,cAAA,GAAAA,cAAA;AAIO,MAAMa,gBAAgB,GAAIG,CAAU,IACzC,IAAAmB,sBAAW,EAACnB,CAAC,EAAEnC,oBAAoB,CAAC;AAEtC;;;;AAAAN,OAAA,CAAAsC,gBAAA,GAAAA,gBAAA;AAIO,MAAMI,YAAY,GAAID,CAAU,IACrC,IAAAmB,sBAAW,EAACnB,CAAC,EAAEjC,gBAAgB,CAAC;AAElC;;;;AAAAR,OAAA,CAAA0C,YAAA,GAAAA,YAAA;AAIO,MAAMM,gBAAgB,GAAIP,CAAU,IACzC,IAAAmB,sBAAW,EAACnB,CAAC,EAAE/B,oBAAoB,CAAC;AAEtC;;;;AAAAV,OAAA,CAAAgD,gBAAA,GAAAA,gBAAA;AAIO,MAAMI,cAAc,GAAIX,CAAU,IAA2C,IAAAmB,sBAAW,EAACnB,CAAC,EAAE7B,kBAAkB,CAAC;AAAAZ,OAAA,CAAAoD,cAAA,GAAAA,cAAA", "ignoreList": []}