import { PrismaClient } from '../../../prisma/generated';
export interface ModuleGraphQL {
    getTypeDefs: () => string;
    getResolvers: (prisma: PrismaClient) => Record<string, any>;
}
export interface LoadedModules {
    typeDefs: string;
    resolvers: Record<string, any>;
}
export declare function loadModules(prisma: PrismaClient): Promise<LoadedModules>;
export declare function createModuleRepository<T>(repositoryFactory: (prisma: PrismaClient) => T, prisma: PrismaClient): T;
//# sourceMappingURL=module-loader.d.ts.map