{"version": 3, "file": "contextPatch.js", "names": ["Chunk", "_interopRequireWildcard", "require", "Equal", "Dual", "_context", "_data", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ContextPatchTypeId", "exports", "Symbol", "for", "variance", "a", "PatchProto", "Structural", "prototype", "_Value", "_Patch", "EmptyProto", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "AddServiceProto", "makeAddService", "key", "service", "RemoveServiceProto", "makeRemoveService", "UpdateServiceProto", "makeUpdateService", "update", "diff", "oldValue", "newValue", "missingServices", "Map", "unsafeMap", "patch", "tag", "newService", "entries", "old", "delete", "equals", "combine", "dual", "self", "that", "context", "wasServiceUpdated", "patches", "of", "updatedContext", "isNonEmpty", "head", "headNonEmpty", "tail", "tailNonEmpty", "prepend", "makeContext", "map", "s"], "sources": ["../../../../src/internal/differ/contextPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,IAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AAAuC,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEvC;AACO,MAAMkB,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,gBAA0BE,MAAM,CAACC,GAAG,CACjE,2BAA2B,CACH;AAE1B,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA;AACA,MAAMC,UAAU,GAAG;EACjB,GAAGC,gBAAU,CAACC,SAAS;EACvB,CAACR,kBAAkB,GAAG;IACpBS,MAAM,EAAEL,QAAQ;IAChBM,MAAM,EAAEN;;CAEX;AAMD,MAAMO,UAAU,gBAAGd,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC1DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGlB,MAAM,CAACgB,MAAM,CAACF,UAAU,CAAC;AAExC;;;AAGO,MAAMK,KAAK,GAAGA,CAAA,KAA0DD,MAAM;AAAAd,OAAA,CAAAe,KAAA,GAAAA,KAAA;AASrF,MAAMC,YAAY,gBAAGpB,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC5DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMI,WAAW,GAAGA,CAClBC,KAA0C,EAC1CC,MAA6C,KACL;EACxC,MAAMjC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACI,YAAY,CAAC;EACrC9B,CAAC,CAACgC,KAAK,GAAGA,KAAK;EACfhC,CAAC,CAACiC,MAAM,GAAGA,MAAM;EACjB,OAAOjC,CAAC;AACV,CAAC;AASD,MAAMkC,eAAe,gBAAGxB,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC/DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMQ,cAAc,GAAGA,CACrBC,GAAW,EACXC,OAAU,KAC4B;EACtC,MAAMrC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACQ,eAAe,CAAC;EACxClC,CAAC,CAACoC,GAAG,GAAGA,GAAG;EACXpC,CAAC,CAACqC,OAAO,GAAGA,OAAO;EACnB,OAAOrC,CAAC;AACV,CAAC;AAQD,MAAMsC,kBAAkB,gBAAG5B,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAClEQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMY,iBAAiB,GACrBH,GAAW,IACmC;EAC9C,MAAMpC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACY,kBAAkB,CAAC;EAC3CtC,CAAC,CAACoC,GAAG,GAAGA,GAAG;EACX,OAAOpC,CAAC;AACV,CAAC;AASD,MAAMwC,kBAAkB,gBAAG9B,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAClEQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMc,iBAAiB,GAAGA,CACxBL,GAAW,EACXM,MAAyB,KACiB;EAC1C,MAAM1C,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACc,kBAAkB,CAAC;EAC3CxC,CAAC,CAACoC,GAAG,GAAGA,GAAG;EACXpC,CAAC,CAAC0C,MAAM,GAAGA,MAAM;EACjB,OAAO1C,CAAC;AACV,CAAC;AASD;AACO,MAAM2C,IAAI,GAAGA,CAClBC,QAAwB,EACxBC,QAAyB,KACc;EACvC,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAACI,SAAS,CAAC;EACnD,IAAIC,KAAK,GAAGpB,KAAK,EAAY;EAC7B,KAAK,MAAM,CAACqB,GAAG,EAAEC,UAAU,CAAC,IAAIN,QAAQ,CAACG,SAAS,CAACI,OAAO,EAAE,EAAE;IAC5D,IAAIN,eAAe,CAACzC,GAAG,CAAC6C,GAAG,CAAC,EAAE;MAC5B,MAAMG,GAAG,GAAGP,eAAe,CAACxC,GAAG,CAAC4C,GAAG,CAAE;MACrCJ,eAAe,CAACQ,MAAM,CAACJ,GAAG,CAAC;MAC3B,IAAI,CAAC5D,KAAK,CAACiE,MAAM,CAACF,GAAG,EAAEF,UAAU,CAAC,EAAE;QAClCF,KAAK,GAAGO,OAAO,CAACf,iBAAiB,CAACS,GAAG,EAAE,MAAMC,UAAU,CAAC,CAAC,CAACF,KAAK,CAAC;MAClE;IACF,CAAC,MAAM;MACLH,eAAe,CAACQ,MAAM,CAACJ,GAAG,CAAC;MAC3BD,KAAK,GAAGO,OAAO,CAACrB,cAAc,CAACe,GAAG,EAAEC,UAAU,CAAC,CAAC,CAACF,KAAK,CAAC;IACzD;EACF;EACA,KAAK,MAAM,CAACC,GAAG,CAAC,IAAIJ,eAAe,CAACM,OAAO,EAAE,EAAE;IAC7CH,KAAK,GAAGO,OAAO,CAACjB,iBAAiB,CAACW,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC;EAChD;EACA,OAAOA,KAAK;AACd,CAAC;AAED;AAAAnC,OAAA,CAAA6B,IAAA,GAAAA,IAAA;AACO,MAAMa,OAAO,GAAA1C,OAAA,CAAA0C,OAAA,gBAAGjE,IAAI,CAACkE,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAK5B,WAAW,CAAC2B,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACO,MAAMV,KAAK,GAAAnC,OAAA,CAAAmC,KAAA,gBAAG1D,IAAI,CAACkE,IAAI,CAU5B,CAAC,EAAE,CAAgBC,IAAyC,EAAEE,OAAuB,KAAI;EACzF,IAAKF,IAAoB,CAAC/B,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOiC,OAAc;EACvB;EACA,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,IAAIC,OAAO,GAAwD3E,KAAK,CAAC4E,EAAE,CACzEL,IAA8C,CAC/C;EACD,MAAMM,cAAc,GAAyB,IAAIjB,GAAG,CAACa,OAAO,CAACZ,SAAS,CAAC;EACvE,OAAO7D,KAAK,CAAC8E,UAAU,CAACH,OAAO,CAAC,EAAE;IAChC,MAAMI,IAAI,GAAgB/E,KAAK,CAACgF,YAAY,CAACL,OAAO,CAAgB;IACpE,MAAMM,IAAI,GAAGjF,KAAK,CAACkF,YAAY,CAACP,OAAO,CAAC;IACxC,QAAQI,IAAI,CAACvC,IAAI;MACf,KAAK,OAAO;QAAE;UACZmC,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,YAAY;QAAE;UACjBJ,cAAc,CAACzD,GAAG,CAAC2D,IAAI,CAAC9B,GAAG,EAAE8B,IAAI,CAAC7B,OAAO,CAAC;UAC1CyB,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdN,OAAO,GAAG3E,KAAK,CAACmF,OAAO,CAACnF,KAAK,CAACmF,OAAO,CAACF,IAAI,EAAEF,IAAI,CAACjC,MAAM,CAAC,EAAEiC,IAAI,CAAClC,KAAK,CAAC;UACrE;QACF;MACA,KAAK,eAAe;QAAE;UACpBgC,cAAc,CAACV,MAAM,CAACY,IAAI,CAAC9B,GAAG,CAAC;UAC/B0B,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,eAAe;QAAE;UACpBJ,cAAc,CAACzD,GAAG,CAAC2D,IAAI,CAAC9B,GAAG,EAAE8B,IAAI,CAACxB,MAAM,CAACsB,cAAc,CAAC1D,GAAG,CAAC4D,IAAI,CAAC9B,GAAG,CAAC,CAAC,CAAC;UACvEyB,iBAAiB,GAAG,IAAI;UACxBC,OAAO,GAAGM,IAAI;UACd;QACF;IACF;EACF;EACA,IAAI,CAACP,iBAAiB,EAAE;IACtB,OAAO,IAAAU,oBAAW,EAACP,cAAc,CAAoB;EACvD;EACA,MAAMQ,GAAG,GAAG,IAAIzB,GAAG,EAAE;EACrB,KAAK,MAAM,CAACG,GAAG,CAAC,IAAIU,OAAO,CAACZ,SAAS,EAAE;IACrC,IAAIgB,cAAc,CAAC3D,GAAG,CAAC6C,GAAG,CAAC,EAAE;MAC3BsB,GAAG,CAACjE,GAAG,CAAC2C,GAAG,EAAEc,cAAc,CAAC1D,GAAG,CAAC4C,GAAG,CAAC,CAAC;MACrCc,cAAc,CAACV,MAAM,CAACJ,GAAG,CAAC;IAC5B;EACF;EACA,KAAK,MAAM,CAACA,GAAG,EAAEuB,CAAC,CAAC,IAAIT,cAAc,EAAE;IACrCQ,GAAG,CAACjE,GAAG,CAAC2C,GAAG,EAAEuB,CAAC,CAAC;EACjB;EACA,OAAO,IAAAF,oBAAW,EAACC,GAAG,CAAoB;AAC5C,CAAC,CAAC", "ignoreList": []}