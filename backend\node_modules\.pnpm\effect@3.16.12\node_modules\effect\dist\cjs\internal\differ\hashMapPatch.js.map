{"version": 3, "file": "hashMapPatch.js", "names": ["Chunk", "_interopRequireWildcard", "require", "Equal", "Dual", "HashMap", "_data", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "HashMapPatchTypeId", "exports", "Symbol", "for", "variance", "a", "PatchProto", "Structural", "prototype", "_Value", "_Key", "_Patch", "EmptyProto", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "AddProto", "makeAdd", "key", "value", "RemoveProto", "makeRemove", "UpdateProto", "makeUpdate", "patch", "diff", "options", "removed", "reduce", "oldValue", "map", "newValue", "option", "valuePatch", "differ", "equals", "remove", "combine", "_", "dual", "self", "that", "patches", "of", "isNonEmpty", "head", "headNonEmpty", "tail", "tailNonEmpty", "prepend"], "sources": ["../../../../src/internal/differ/hashMapPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,IAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AAAuC,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEvC;AACO,MAAMkB,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,gBAAiCE,MAAM,CAACC,GAAG,CACxE,2BAA2B,CACI;AAEjC,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA;AACA,MAAMC,UAAU,GAAG;EACjB,GAAGC,gBAAU,CAACC,SAAS;EACvB,CAACR,kBAAkB,GAAG;IACpBS,MAAM,EAAEL,QAAQ;IAChBM,IAAI,EAAEN,QAAQ;IACdO,MAAM,EAAEP;;CAEX;AAMD,MAAMQ,UAAU,gBAAGf,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC1DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGnB,MAAM,CAACiB,MAAM,CAACF,UAAU,CAAC;AAExC;AACO,MAAMK,KAAK,GAAGA,CAAA,KAAyED,MAAM;AAAAf,OAAA,CAAAgB,KAAA,GAAAA,KAAA;AAQpG,MAAMC,YAAY,gBAAGrB,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMI,WAAW,GAAGA,CAClBC,KAAqD,EACrDC,MAAsD,KACJ;EAClD,MAAMlC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACI,YAAY,CAAC;EACrC/B,CAAC,CAACiC,KAAK,GAAGA,KAAK;EACfjC,CAAC,CAACkC,MAAM,GAAGA,MAAM;EACjB,OAAOlC,CAAC;AACV,CAAC;AAQD,MAAMmC,QAAQ,gBAAGzB,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EACxDS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMQ,OAAO,GAAGA,CAAoBC,GAAQ,EAAEC,KAAY,KAAoD;EAC5G,MAAMtC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACQ,QAAQ,CAAC;EACjCnC,CAAC,CAACqC,GAAG,GAAGA,GAAG;EACXrC,CAAC,CAACsC,KAAK,GAAGA,KAAK;EACf,OAAOtC,CAAC;AACV,CAAC;AAOD,MAAMuC,WAAW,gBAAG7B,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC3DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMY,UAAU,GAAuBH,GAAQ,IAAoD;EACjG,MAAMrC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACY,WAAW,CAAC;EACpCvC,CAAC,CAACqC,GAAG,GAAGA,GAAG;EACX,OAAOrC,CAAC;AACV,CAAC;AAQD,MAAMyC,WAAW,gBAAG/B,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC3DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMc,UAAU,GAAGA,CAAoBL,GAAQ,EAAEM,KAAY,KAAoD;EAC/G,MAAM3C,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACc,WAAW,CAAC;EACpCzC,CAAC,CAACqC,GAAG,GAAGA,GAAG;EACXrC,CAAC,CAAC2C,KAAK,GAAGA,KAAK;EACf,OAAO3C,CAAC;AACV,CAAC;AASD;AACO,MAAM4C,IAAI,GACfC,OAIC,IACiD;EAClD,MAAM,CAACC,OAAO,EAAEH,KAAK,CAAC,GAAGnD,OAAO,CAACuD,MAAM,CACrC,CAACF,OAAO,CAACG,QAAQ,EAAElB,KAAK,EAAqB,CAAU,EACvD,CAAC,CAACmB,GAAG,EAAEN,KAAK,CAAC,EAAEO,QAAe,EAAEb,GAAQ,KAAI;IAC1C,MAAMc,MAAM,GAAG3D,OAAO,CAACc,GAAG,CAAC+B,GAAG,CAAC,CAACY,GAAG,CAAC;IACpC,QAAQE,MAAM,CAACvB,IAAI;MACjB,KAAK,MAAM;QAAE;UACX,MAAMwB,UAAU,GAAGP,OAAO,CAACQ,MAAM,CAACT,IAAI,CAACO,MAAM,CAACb,KAAK,EAAEY,QAAQ,CAAC;UAC9D,IAAI5D,KAAK,CAACgE,MAAM,CAACF,UAAU,EAAEP,OAAO,CAACQ,MAAM,CAACvB,KAAK,CAAC,EAAE;YAClD,OAAO,CAACtC,OAAO,CAAC+D,MAAM,CAAClB,GAAG,CAAC,CAACY,GAAG,CAAC,EAAEN,KAAK,CAAU;UACnD;UACA,OAAO,CACLnD,OAAO,CAAC+D,MAAM,CAAClB,GAAG,CAAC,CAACY,GAAG,CAAC,EACxBO,OAAO,CAAoBd,UAAU,CAACL,GAAG,EAAEe,UAAU,CAAC,CAAC,CAACT,KAAK,CAAC,CACtD;QACZ;MACA,KAAK,MAAM;QAAE;UACX,OAAO,CAACM,GAAG,EAAEO,OAAO,CAAoBpB,OAAO,CAACC,GAAG,EAAEa,QAAQ,CAAC,CAAC,CAACP,KAAK,CAAC,CAAU;QAClF;IACF;EACF,CAAC,CACF,CAACE,OAAO,CAACK,QAAQ,CAAC;EACnB,OAAO1D,OAAO,CAACuD,MAAM,CACnBJ,KAAK,EACL,CAACA,KAAK,EAAEc,CAAC,EAAEpB,GAAQ,KAAKmB,OAAO,CAAoBhB,UAAU,CAACH,GAAG,CAAC,CAAC,CAACM,KAAK,CAAC,CAC3E,CAACG,OAAO,CAAC;AACZ,CAAC;AAED;AAAAhC,OAAA,CAAA8B,IAAA,GAAAA,IAAA;AACO,MAAMY,OAAO,GAAA1C,OAAA,CAAA0C,OAAA,gBAAGjE,IAAI,CAACmE,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAK5B,WAAW,CAAC2B,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACO,MAAMjB,KAAK,GAAA7B,OAAA,CAAA6B,KAAA,gBAAGpD,IAAI,CAACmE,IAAI,CAY5B,CAAC,EAAE,CACHC,IAAoD,EACpDX,QAAqC,EACrCK,MAAmC,KACjC;EACF,IAAKM,IAAoB,CAAC/B,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOoB,QAAQ;EACjB;EACA,IAAIC,GAAG,GAAGD,QAAQ;EAClB,IAAIa,OAAO,GAAgE1E,KAAK,CAAC2E,EAAE,CAACH,IAAI,CAAC;EACzF,OAAOxE,KAAK,CAAC4E,UAAU,CAACF,OAAO,CAAC,EAAE;IAChC,MAAMG,IAAI,GAAgB7E,KAAK,CAAC8E,YAAY,CAACJ,OAAO,CAAgB;IACpE,MAAMK,IAAI,GAAG/E,KAAK,CAACgF,YAAY,CAACN,OAAO,CAAC;IACxC,QAAQG,IAAI,CAACpC,IAAI;MACf,KAAK,OAAO;QAAE;UACZiC,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdL,OAAO,GAAG1E,KAAK,CAACiF,OAAO,CAACJ,IAAI,CAAC/B,KAAK,CAAC,CAAC9C,KAAK,CAACiF,OAAO,CAACJ,IAAI,CAAC9B,MAAM,CAAC,CAACgC,IAAI,CAAC,CAAC;UACrE;QACF;MACA,KAAK,KAAK;QAAE;UACVjB,GAAG,GAAGzD,OAAO,CAACe,GAAG,CAACyD,IAAI,CAAC3B,GAAG,EAAE2B,IAAI,CAAC1B,KAAK,CAAC,CAACW,GAAG,CAAC;UAC5CY,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACbjB,GAAG,GAAGzD,OAAO,CAAC+D,MAAM,CAACS,IAAI,CAAC3B,GAAG,CAAC,CAACY,GAAG,CAAC;UACnCY,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACb,MAAMf,MAAM,GAAG3D,OAAO,CAACc,GAAG,CAAC0D,IAAI,CAAC3B,GAAG,CAAC,CAACY,GAAG,CAAC;UACzC,IAAIE,MAAM,CAACvB,IAAI,KAAK,MAAM,EAAE;YAC1BqB,GAAG,GAAGzD,OAAO,CAACe,GAAG,CAACyD,IAAI,CAAC3B,GAAG,EAAEgB,MAAM,CAACV,KAAK,CAACqB,IAAI,CAACrB,KAAK,EAAEQ,MAAM,CAACb,KAAK,CAAC,CAAC,CAACW,GAAG,CAAC;UAC1E;UACAY,OAAO,GAAGK,IAAI;UACd;QACF;IACF;EACF;EACA,OAAOjB,GAAG;AACZ,CAAC,CAAC", "ignoreList": []}