{"version": 3, "file": "module-loader.js", "sourceRoot": "", "sources": ["../../../../src/shared/kernel/module-loader.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,kCA6EC;AAED,wDAKC;AApHD,2BAA6C;AAC7C,+BAA4B;AAE5B,qCAAkC;AAYlC,MAAM,YAAY,GAAG;;;;;;;;;;;;;;;CAepB,CAAC;AAEK,KAAK,UAAU,WAAW,CAAC,MAAoB;IACpD,MAAM,UAAU,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;IAEvD,IAAI,CAAC,IAAA,eAAU,EAAC,UAAU,CAAC,EAAE,CAAC;QAC5B,eAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAClE,OAAO;YACL,QAAQ,EAAE,YAAY;YACtB,SAAS,EAAE;gBACT,KAAK,EAAE;oBACL,MAAM,EAAE,GAAG,EAAE,CAAC,2BAA2B;iBAC1C;gBACD,QAAQ,EAAE;oBACR,MAAM,EAAE,GAAG,EAAE,CAAC,2BAA2B;iBAC1C;aACF;SACF,CAAC;IACJ,CAAC;IAED,MAAM,WAAW,GAAG,IAAA,gBAAW,EAAC,UAAU,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;SACjE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;SACtC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAE9B,MAAM,WAAW,GAAa,CAAC,YAAY,CAAC,CAAC;IAC7C,MAAM,YAAY,GAAwB;QACxC,KAAK,EAAE;YACL,MAAM,EAAE,GAAG,EAAE,CAAC,2BAA2B;SAC1C;QACD,QAAQ,EAAE;YACR,MAAM,EAAE,GAAG,EAAE,CAAC,2BAA2B;SAC1C;KACF,CAAC;IAEF,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;QACrC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAA,WAAI,EAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;YAExE,IAAI,IAAA,eAAU,EAAC,WAAW,CAAC,EAAE,CAAC;gBAC5B,eAAM,CAAC,IAAI,CAAC,2BAA2B,UAAU,EAAE,CAAC,CAAC;gBAGrD,MAAM,aAAa,GAAkB,yBAAa,WAAW,uCAAC,CAAC;gBAE/D,IAAI,OAAO,aAAa,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;oBACpD,MAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;oBAC7C,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC7B,CAAC;gBAED,IAAI,OAAO,aAAa,CAAC,YAAY,KAAK,UAAU,EAAE,CAAC;oBACrD,MAAM,SAAS,GAAG,aAAa,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;oBAGrD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACpC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;4BACxB,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;wBAC1B,CAAC;wBACD,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oBACrD,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,eAAM,CAAC,IAAI,CAAC,iCAAiC,UAAU,EAAE,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,eAAM,CAAC,KAAK,CAAC,uCAAuC,UAAU,EAAE,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,2BAA2B,UAAU,EAAE,CAAC,CAAC;QAE/E,CAAC;IACH,CAAC;IAED,MAAM,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAElD,eAAM,CAAC,IAAI,CAAC,aAAa,WAAW,CAAC,MAAM,+BAA+B,CAAC,CAAC;IAE5E,OAAO;QACL,QAAQ,EAAE,gBAAgB;QAC1B,SAAS,EAAE,YAAY;KACxB,CAAC;AACJ,CAAC;AAED,SAAgB,sBAAsB,CACpC,iBAA8C,EAC9C,MAAoB;IAEpB,OAAO,iBAAiB,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC"}