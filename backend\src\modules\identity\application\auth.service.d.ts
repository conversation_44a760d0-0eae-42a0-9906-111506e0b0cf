import { UserRepository } from '../infra/user.repository';
import { LoginInput, RegisterInput, AuthPayload, JWTPayload } from '../domain/types';
export interface AuthService {
    register(input: RegisterInput): Promise<AuthPayload>;
    login(input: LoginInput): Promise<AuthPayload>;
    verifyToken(token: string): Promise<JWTPayload>;
    refreshToken(token: string): Promise<string>;
}
export declare const createAuthService: (userRepository: UserRepository, jwtSecret?: string) => AuthService;
//# sourceMappingURL=auth.service.d.ts.map