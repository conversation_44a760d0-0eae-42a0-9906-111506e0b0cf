{"version": 3, "file": "orPatch.js", "names": ["Chunk", "_interopRequireWildcard", "require", "E", "Equal", "Dual", "_data", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "OrPatchTypeId", "exports", "Symbol", "for", "variance", "a", "PatchProto", "Structural", "prototype", "_Value", "_Key", "_Patch", "EmptyProto", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "SetLeftProto", "makeSetLeft", "value", "SetRightProto", "makeSetRight", "UpdateLeftProto", "makeUpdateLeft", "patch", "UpdateRightProto", "makeUpdateRight", "diff", "options", "oldValue", "newValue", "valuePatch", "left", "equals", "right", "combine", "dual", "self", "that", "patches", "of", "result", "isNonEmpty", "head", "headNonEmpty", "tail", "tailNonEmpty", "prepend"], "sources": ["../../../../src/internal/differ/orPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,CAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,KAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AAAuC,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEvC;AACO,MAAMkB,aAAa,GAAAC,OAAA,CAAAD,aAAA,gBAAqBE,MAAM,CAACC,GAAG,CAAC,sBAAsB,CAAqB;AAErG,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA;AACA,MAAMC,UAAU,GAAG;EACjB,GAAGC,gBAAU,CAACC,SAAS;EACvB,CAACR,aAAa,GAAG;IACfS,MAAM,EAAEL,QAAQ;IAChBM,IAAI,EAAEN,QAAQ;IACdO,MAAM,EAAEP;;CAEX;AASD,MAAMQ,UAAU,gBAAGf,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC1DS,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGnB,MAAM,CAACiB,MAAM,CAACF,UAAU,CAAC;AAExC;AACO,MAAMK,KAAK,GAAGA,CAAA,KAKhBD,MAAM;AAAAf,OAAA,CAAAgB,KAAA,GAAAA,KAAA;AAWX,MAAMC,YAAY,gBAAGrB,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMI,WAAW,GAAGA,CACzBC,KAAoD,EACpDC,MAAqD,KAMnD;EACF,MAAMlC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACI,YAAY,CAAC;EACrC/B,CAAC,CAACiC,KAAK,GAAGA,KAAK;EACfjC,CAAC,CAACkC,MAAM,GAAGA,MAAM;EACjB,OAAOlC,CAAC;AACV,CAAC;AAAAc,OAAA,CAAAkB,WAAA,GAAAA,WAAA;AAUD,MAAMG,YAAY,gBAAGzB,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC5DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMQ,WAAW,GACtBC,KAAY,IAMV;EACF,MAAMrC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACQ,YAAY,CAAC;EACrCnC,CAAC,CAACqC,KAAK,GAAGA,KAAK;EACf,OAAOrC,CAAC;AACV,CAAC;AAAAc,OAAA,CAAAsB,WAAA,GAAAA,WAAA;AAUD,MAAME,aAAa,gBAAG5B,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC7DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMW,YAAY,GACvBF,KAAa,IAMX;EACF,MAAMrC,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACW,aAAa,CAAC;EACtCtC,CAAC,CAACqC,KAAK,GAAGA,KAAK;EACf,OAAOrC,CAAC;AACV,CAAC;AAAAc,OAAA,CAAAyB,YAAA,GAAAA,YAAA;AAUD,MAAMC,eAAe,gBAAG9B,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAC/DS,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMa,cAAc,GACzBC,KAAY,IAMV;EACF,MAAM1C,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACa,eAAe,CAAC;EACxCxC,CAAC,CAAC0C,KAAK,GAAGA,KAAK;EACf,OAAO1C,CAAC;AACV,CAAC;AAAAc,OAAA,CAAA2B,cAAA,GAAAA,cAAA;AAUD,MAAME,gBAAgB,gBAAGjC,MAAM,CAACgB,MAAM,cAAChB,MAAM,CAACiB,MAAM,CAACR,UAAU,CAAC,EAAE;EAChES,IAAI,EAAE;CACP,CAAC;AAEF;AACO,MAAMgB,eAAe,GAC1BF,KAAa,IAMX;EACF,MAAM1C,CAAC,GAAGU,MAAM,CAACiB,MAAM,CAACgB,gBAAgB,CAAC;EACzC3C,CAAC,CAAC0C,KAAK,GAAGA,KAAK;EACf,OAAO1C,CAAC;AACV,CAAC;AAUD;AAAAc,OAAA,CAAA8B,eAAA,GAAAA,eAAA;AACO,MAAMC,IAAI,GACfC,OAKC,IACgD;EACjD,QAAQA,OAAO,CAACC,QAAQ,CAACnB,IAAI;IAC3B,KAAK,MAAM;MAAE;QACX,QAAQkB,OAAO,CAACE,QAAQ,CAACpB,IAAI;UAC3B,KAAK,MAAM;YAAE;cACX,MAAMqB,UAAU,GAAGH,OAAO,CAACI,IAAI,CAACL,IAAI,CAACC,OAAO,CAACC,QAAQ,CAACG,IAAI,EAAEJ,OAAO,CAACE,QAAQ,CAACE,IAAI,CAAC;cAClF,IAAI3D,KAAK,CAAC4D,MAAM,CAACF,UAAU,EAAEH,OAAO,CAACI,IAAI,CAACpB,KAAK,CAAC,EAAE;gBAChD,OAAOA,KAAK,EAAE;cAChB;cACA,OAAOW,cAAc,CAACQ,UAAU,CAAC;YACnC;UACA,KAAK,OAAO;YAAE;cACZ,OAAOV,YAAY,CAACO,OAAO,CAACE,QAAQ,CAACI,KAAK,CAAC;YAC7C;QACF;MACF;IACA,KAAK,OAAO;MAAE;QACZ,QAAQN,OAAO,CAACE,QAAQ,CAACpB,IAAI;UAC3B,KAAK,MAAM;YAAE;cACX,OAAOQ,WAAW,CAACU,OAAO,CAACE,QAAQ,CAACE,IAAI,CAAC;YAC3C;UACA,KAAK,OAAO;YAAE;cACZ,MAAMD,UAAU,GAAGH,OAAO,CAACM,KAAK,CAACP,IAAI,CAACC,OAAO,CAACC,QAAQ,CAACK,KAAK,EAAEN,OAAO,CAACE,QAAQ,CAACI,KAAK,CAAC;cACrF,IAAI7D,KAAK,CAAC4D,MAAM,CAACF,UAAU,EAAEH,OAAO,CAACM,KAAK,CAACtB,KAAK,CAAC,EAAE;gBACjD,OAAOA,KAAK,EAAE;cAChB;cACA,OAAOc,eAAe,CAACK,UAAU,CAAC;YACpC;QACF;MACF;EACF;AACF,CAAC;AAED;AAAAnC,OAAA,CAAA+B,IAAA,GAAAA,IAAA;AACO,MAAMQ,OAAO,GAAAvC,OAAA,CAAAuC,OAAA,gBAAG7D,IAAI,CAAC8D,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAKxB,WAAW,CAACuB,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACO,MAAMd,KAAK,GAAA5B,OAAA,CAAA4B,KAAA,gBAAGlD,IAAI,CAAC8D,IAAI,CAgB5B,CAAC,EAAE,CACHC,IAAmD,EACnD;EAAEL,IAAI;EAAEH,QAAQ;EAAEK;AAAK,CAItB,KACC;EACF,IAAKG,IAAoB,CAAC3B,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOmB,QAAQ;EACjB;EACA,IAAIU,OAAO,GAA+DtE,KAAK,CAACuE,EAAE,CAACH,IAAI,CAAC;EACxF,IAAII,MAAM,GAAGZ,QAAQ;EACrB,OAAO5D,KAAK,CAACyE,UAAU,CAACH,OAAO,CAAC,EAAE;IAChC,MAAMI,IAAI,GAAgB1E,KAAK,CAAC2E,YAAY,CAACL,OAAO,CAAgB;IACpE,MAAMM,IAAI,GAAG5E,KAAK,CAAC6E,YAAY,CAACP,OAAO,CAAC;IACxC,QAAQI,IAAI,CAACjC,IAAI;MACf,KAAK,OAAO;QAAE;UACZ6B,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdN,OAAO,GAAGtE,KAAK,CAAC8E,OAAO,CAACJ,IAAI,CAAC5B,KAAK,CAAC,CAAC9C,KAAK,CAAC8E,OAAO,CAACJ,IAAI,CAAC3B,MAAM,CAAC,CAAC6B,IAAI,CAAC,CAAC;UACrE;QACF;MACA,KAAK,YAAY;QAAE;UACjB,IAAIJ,MAAM,CAAC/B,IAAI,KAAK,MAAM,EAAE;YAC1B+B,MAAM,GAAGrE,CAAC,CAAC4D,IAAI,CAACA,IAAI,CAACR,KAAK,CAACmB,IAAI,CAACnB,KAAK,EAAEiB,MAAM,CAACT,IAAI,CAAC,CAAC;UACtD;UACAO,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,aAAa;QAAE;UAClB,IAAIJ,MAAM,CAAC/B,IAAI,KAAK,OAAO,EAAE;YAC3B+B,MAAM,GAAGrE,CAAC,CAAC8D,KAAK,CAACA,KAAK,CAACV,KAAK,CAACmB,IAAI,CAACnB,KAAK,EAAEiB,MAAM,CAACP,KAAK,CAAC,CAAC;UACzD;UACAK,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdJ,MAAM,GAAGrE,CAAC,CAAC4D,IAAI,CAACW,IAAI,CAACxB,KAAK,CAAC;UAC3BoB,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,UAAU;QAAE;UACfJ,MAAM,GAAGrE,CAAC,CAAC8D,KAAK,CAACS,IAAI,CAACxB,KAAK,CAAC;UAC5BoB,OAAO,GAAGM,IAAI;UACd;QACF;IACF;EACF;EACA,OAAOJ,MAAM;AACf,CAAC,CAAC", "ignoreList": []}