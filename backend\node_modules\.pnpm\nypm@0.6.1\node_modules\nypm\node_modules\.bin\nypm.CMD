@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\nypm@0.6.1\node_modules\nypm\dist\node_modules;C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\nypm@0.6.1\node_modules\nypm\node_modules;C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\nypm@0.6.1\node_modules;C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\nypm@0.6.1\node_modules\nypm\dist\node_modules;C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\nypm@0.6.1\node_modules\nypm\node_modules;C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\nypm@0.6.1\node_modules;C:\Users\<USER>\Desktop\edu\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\dist\cli.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\dist\cli.mjs" %*
)
