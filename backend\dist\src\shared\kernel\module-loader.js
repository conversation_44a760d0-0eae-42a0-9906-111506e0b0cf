"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadModules = loadModules;
exports.createModuleRepository = createModuleRepository;
const fs_1 = require("fs");
const path_1 = require("path");
const logger_1 = require("./logger");
const baseTypeDefs = `
  scalar DateTime
  scalar JSON

  type Query {
    _empty: String
  }

  type Mutation {
    _empty: String
  }

  type Subscription {
    _empty: String
  }
`;
async function loadModules(prisma) {
    const modulesDir = (0, path_1.join)(__dirname, '../../../modules');
    if (!(0, fs_1.existsSync)(modulesDir)) {
        logger_1.logger.info('No modules directory found, using base schema only');
        return {
            typeDefs: baseTypeDefs,
            resolvers: {
                Query: {
                    _empty: () => 'GraphQL server is running',
                },
                Mutation: {
                    _empty: () => 'GraphQL server is running',
                },
            },
        };
    }
    const moduleNames = (0, fs_1.readdirSync)(modulesDir, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
    const allTypeDefs = [baseTypeDefs];
    const allResolvers = {
        Query: {
            _empty: () => 'GraphQL server is running',
        },
        Mutation: {
            _empty: () => 'GraphQL server is running',
        },
    };
    for (const moduleName of moduleNames) {
        try {
            const graphqlPath = (0, path_1.join)(modulesDir, moduleName, 'graphql', 'index.ts');
            if ((0, fs_1.existsSync)(graphqlPath)) {
                logger_1.logger.info(`Loading GraphQL module: ${moduleName}`);
                const moduleGraphQL = await Promise.resolve(`${graphqlPath}`).then(s => __importStar(require(s)));
                if (typeof moduleGraphQL.getTypeDefs === 'function') {
                    const typeDefs = moduleGraphQL.getTypeDefs();
                    allTypeDefs.push(typeDefs);
                }
                if (typeof moduleGraphQL.getResolvers === 'function') {
                    const resolvers = moduleGraphQL.getResolvers(prisma);
                    Object.keys(resolvers).forEach(type => {
                        if (!allResolvers[type]) {
                            allResolvers[type] = {};
                        }
                        Object.assign(allResolvers[type], resolvers[type]);
                    });
                }
                logger_1.logger.info(`✅ Successfully loaded module: ${moduleName}`);
            }
            else {
                logger_1.logger.debug(`No GraphQL schema found for module: ${moduleName}`);
            }
        }
        catch (error) {
            logger_1.logger.error({ error, moduleName }, `❌ Failed to load module ${moduleName}`);
        }
    }
    const combinedTypeDefs = allTypeDefs.join('\n\n');
    logger_1.logger.info(`🚀 Loaded ${moduleNames.length} modules with GraphQL schemas`);
    return {
        typeDefs: combinedTypeDefs,
        resolvers: allResolvers,
    };
}
function createModuleRepository(repositoryFactory, prisma) {
    return repositoryFactory(prisma);
}
//# sourceMappingURL=module-loader.js.map