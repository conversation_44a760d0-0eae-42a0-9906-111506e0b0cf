{"version": 3, "file": "registry.js", "names": ["_Function", "require", "MutableHashMap", "_interopRequireWildcard", "Option", "metricHook", "metricKeyType", "metricPair", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "MetricRegistrySymbolKey", "MetricRegistryTypeId", "exports", "Symbol", "for", "MetricRegistryImpl", "map", "empty", "snapshot", "result", "key", "hook", "push", "unsafeMake", "pipe", "getOrUndefined", "isCounterKey", "keyType", "get<PERSON>ounter", "isGaugeKey", "getGauge", "isFrequencyKey", "getFrequency", "isHistogramKey", "getHistogram", "isSummary<PERSON>ey", "getSummary", "Error", "value", "counter", "frequency", "gauge", "bigint", "BigInt", "histogram", "summary", "make"], "sources": ["../../../../src/internal/metric/registry.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,OAAA;AAMA,IAAAC,cAAA,GAAAC,uBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAD,uBAAA,CAAAF,OAAA;AACA,IAAAI,UAAA,GAAAF,uBAAA,CAAAF,OAAA;AACA,IAAAK,aAAA,GAAAH,uBAAA,CAAAF,OAAA;AACA,IAAAM,UAAA,GAAAJ,uBAAA,CAAAF,OAAA;AAAuC,SAAAE,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEvC;AACA,MAAMkB,uBAAuB,GAAG,uBAAuB;AAEvD;AACO,MAAMC,oBAAoB,GAAAC,OAAA,CAAAD,oBAAA,gBAAwCE,MAAM,CAACC,GAAG,CACjFJ,uBAAuB,CACe;AAExC;AACA,MAAMK,kBAAkB;EACb,CAACJ,oBAAoB,IAAyCA,oBAAoB;EAEnFK,GAAG,gBAAG/B,cAAc,CAACgC,KAAK,EAG/B;EAEHC,QAAQA,CAAA;IACN,MAAMC,MAAM,GAAyC,EAAE;IACvD,KAAK,MAAM,CAACC,GAAG,EAAEC,IAAI,CAAC,IAAI,IAAI,CAACL,GAAG,EAAE;MAClCG,MAAM,CAACG,IAAI,CAAChC,UAAU,CAACiC,UAAU,CAACH,GAAG,EAAEC,IAAI,CAAClB,GAAG,EAAE,CAAC,CAAC;IACrD;IACA,OAAOgB,MAAM;EACf;EAEAhB,GAAGA,CACDiB,GAA8B;IAK9B,MAAMC,IAAI,GAAG,IAAAG,cAAI,EACf,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACkB,GAAG,CAACiB,GAA+D,CAAC,EACnFjC,MAAM,CAACsC,cAAc,CACtB;IACD,IAAIJ,IAAI,IAAI,IAAI,EAAE;MAChB,IAAIhC,aAAa,CAACqC,YAAY,CAACN,GAAG,CAACO,OAAO,CAAC,EAAE;QAC3C,OAAO,IAAI,CAACC,UAAU,CAACR,GAAkD,CAAQ;MACnF;MACA,IAAI/B,aAAa,CAACwC,UAAU,CAACT,GAAG,CAACO,OAAO,CAAC,EAAE;QACzC,OAAO,IAAI,CAACG,QAAQ,CAACV,GAAgD,CAAQ;MAC/E;MACA,IAAI/B,aAAa,CAAC0C,cAAc,CAACX,GAAG,CAACO,OAAO,CAAC,EAAE;QAC7C,OAAO,IAAI,CAACK,YAAY,CAACZ,GAA+C,CAAQ;MAClF;MACA,IAAI/B,aAAa,CAAC4C,cAAc,CAACb,GAAG,CAACO,OAAO,CAAC,EAAE;QAC7C,OAAO,IAAI,CAACO,YAAY,CAACd,GAA+C,CAAQ;MAClF;MACA,IAAI/B,aAAa,CAAC8C,YAAY,CAACf,GAAG,CAACO,OAAO,CAAC,EAAE;QAC3C,OAAO,IAAI,CAACS,UAAU,CAAChB,GAA6C,CAAQ;MAC9E;MACA,MAAM,IAAIiB,KAAK,CACb,wHAAwH,CACzH;IACH,CAAC,MAAM;MACL,OAAOhB,IAAW;IACpB;EACF;EAEAO,UAAUA,CAA8BR,GAAmC;IACzE,IAAIkB,KAAK,GAAG,IAAAd,cAAI,EACd,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACkB,GAAG,CAACiB,GAA+D,CAAC,EACnFjC,MAAM,CAACsC,cAAc,CACtB;IACD,IAAIa,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMC,OAAO,GAAGnD,UAAU,CAACmD,OAAO,CAACnB,GAAG,CAAC;MACvC,IAAI,CAAC,IAAAI,cAAI,EAAC,IAAI,CAACR,GAAG,EAAE/B,cAAc,CAACiB,GAAG,CAACkB,GAA+D,CAAC,CAAC,EAAE;QACxG,IAAAI,cAAI,EACF,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACmB,GAAG,CAChBgB,GAA+D,EAC/DmB,OAAqC,CACtC,CACF;MACH;MACAD,KAAK,GAAGC,OAAO;IACjB;IACA,OAAOD,KAAyC;EAClD;EAEAN,YAAYA,CAACZ,GAAkC;IAC7C,IAAIkB,KAAK,GAAG,IAAAd,cAAI,EACd,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACkB,GAAG,CAACiB,GAA+D,CAAC,EACnFjC,MAAM,CAACsC,cAAc,CACtB;IACD,IAAIa,KAAK,IAAI,IAAI,EAAE;MACjB,MAAME,SAAS,GAAGpD,UAAU,CAACoD,SAAS,CAACpB,GAAG,CAAC;MAC3C,IAAI,CAAC,IAAAI,cAAI,EAAC,IAAI,CAACR,GAAG,EAAE/B,cAAc,CAACiB,GAAG,CAACkB,GAA+D,CAAC,CAAC,EAAE;QACxG,IAAAI,cAAI,EACF,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACmB,GAAG,CAChBgB,GAA+D,EAC/DoB,SAAuC,CACxC,CACF;MACH;MACAF,KAAK,GAAGE,SAAS;IACnB;IACA,OAAOF,KAAwC;EACjD;EAEAR,QAAQA,CAA8BV,GAAiC;IACrE,IAAIkB,KAAK,GAAG,IAAAd,cAAI,EACd,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACkB,GAAG,CAACiB,GAA+D,CAAC,EACnFjC,MAAM,CAACsC,cAAc,CACtB;IACD,IAAIa,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMG,KAAK,GAAGrD,UAAU,CAACqD,KAAK,CAACrB,GAAU,EAAEA,GAAG,CAACO,OAAO,CAACe,MAAM,GAAGC,MAAM,CAAC,CAAC,CAAQ,GAAG,CAAC,CAAC;MACrF,IAAI,CAAC,IAAAnB,cAAI,EAAC,IAAI,CAACR,GAAG,EAAE/B,cAAc,CAACiB,GAAG,CAACkB,GAA+D,CAAC,CAAC,EAAE;QACxG,IAAAI,cAAI,EACF,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACmB,GAAG,CAChBgB,GAA+D,EAC/DqB,KAAmC,CACpC,CACF;MACH;MACAH,KAAK,GAAGG,KAAK;IACf;IACA,OAAOH,KAAuC;EAChD;EAEAJ,YAAYA,CAACd,GAAkC;IAC7C,IAAIkB,KAAK,GAAG,IAAAd,cAAI,EACd,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACkB,GAAG,CAACiB,GAA+D,CAAC,EACnFjC,MAAM,CAACsC,cAAc,CACtB;IACD,IAAIa,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMM,SAAS,GAAGxD,UAAU,CAACwD,SAAS,CAACxB,GAAG,CAAC;MAC3C,IAAI,CAAC,IAAAI,cAAI,EAAC,IAAI,CAACR,GAAG,EAAE/B,cAAc,CAACiB,GAAG,CAACkB,GAA+D,CAAC,CAAC,EAAE;QACxG,IAAAI,cAAI,EACF,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACmB,GAAG,CAChBgB,GAA+D,EAC/DwB,SAAuC,CACxC,CACF;MACH;MACAN,KAAK,GAAGM,SAAS;IACnB;IACA,OAAON,KAAwC;EACjD;EAEAF,UAAUA,CAAChB,GAAgC;IACzC,IAAIkB,KAAK,GAAG,IAAAd,cAAI,EACd,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACkB,GAAG,CAACiB,GAA+D,CAAC,EACnFjC,MAAM,CAACsC,cAAc,CACtB;IACD,IAAIa,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMO,OAAO,GAAGzD,UAAU,CAACyD,OAAO,CAACzB,GAAG,CAAC;MACvC,IAAI,CAAC,IAAAI,cAAI,EAAC,IAAI,CAACR,GAAG,EAAE/B,cAAc,CAACiB,GAAG,CAACkB,GAA+D,CAAC,CAAC,EAAE;QACxG,IAAAI,cAAI,EACF,IAAI,CAACR,GAAG,EACR/B,cAAc,CAACmB,GAAG,CAChBgB,GAA+D,EAC/DyB,OAAqC,CACtC,CACF;MACH;MACAP,KAAK,GAAGO,OAAO;IACjB;IACA,OAAOP,KAAsC;EAC/C;;AAGF;AACO,MAAMQ,IAAI,GAAGA,CAAA,KAAoC;EACtD,OAAO,IAAI/B,kBAAkB,EAAE;AACjC,CAAC;AAAAH,OAAA,CAAAkC,IAAA,GAAAA,IAAA", "ignoreList": []}