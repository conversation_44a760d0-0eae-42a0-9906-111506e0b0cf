{"version": 3, "file": "readonlyArrayPatch.js", "names": ["Arr", "_interopRequireWildcard", "require", "Equal", "Dual", "Data", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ReadonlyArrayPatchTypeId", "exports", "Symbol", "for", "variance", "a", "PatchProto", "Structural", "prototype", "_Value", "_Patch", "EmptyProto", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "AppendProto", "makeAppend", "values", "SliceProto", "makeSlice", "from", "until", "UpdateProto", "makeUpdate", "index", "patch", "diff", "options", "oldValue", "length", "newValue", "oldElement", "newElement", "valuePatch", "differ", "equals", "combine", "drop", "dual", "self", "that", "readon<PERSON><PERSON><PERSON><PERSON>", "slice", "patches", "of", "isNonEmptyArray", "head", "headNonEmpty", "tail", "tailNonEmpty", "unshift", "value", "push"], "sources": ["../../../../src/internal/differ/readonlyArrayPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,IAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAkC,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAElC;AACO,MAAMkB,wBAAwB,GAAAC,OAAA,CAAAD,wBAAA,gBAAuCE,MAAM,CAACC,GAAG,CACpF,iCAAiC,CACI;AAEvC,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA,MAAMC,UAAU,GAAG;EACjB,GAAG1B,IAAI,CAAC2B,UAAU,CAACC,SAAS;EAC5B,CAACR,wBAAwB,GAAG;IAC1BS,MAAM,EAAEL,QAAQ;IAChBM,MAAM,EAAEN;;CAEX;AAMD,MAAMO,UAAU,gBAAGd,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC1DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGlB,MAAM,CAACgB,MAAM,CAACF,UAAU,CAAC;AAExC;;;AAGO,MAAMK,KAAK,GAAGA,CAAA,KAAqED,MAAM;AAAAd,OAAA,CAAAe,KAAA,GAAAA,KAAA;AAQhG,MAAMC,YAAY,gBAAGpB,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC5DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMI,WAAW,GAAGA,CAClBC,KAAsD,EACtDC,MAAuD,KACJ;EACnD,MAAMjC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACI,YAAY,CAAC;EACrC9B,CAAC,CAACgC,KAAK,GAAGA,KAAK;EACfhC,CAAC,CAACiC,MAAM,GAAGA,MAAM;EACjB,OAAOjC,CAAC;AACV,CAAC;AAOD,MAAMkC,WAAW,gBAAGxB,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC3DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMQ,UAAU,GAAkBC,MAA4B,IAAqD;EACjH,MAAMpC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACQ,WAAW,CAAC;EACpClC,CAAC,CAACoC,MAAM,GAAGA,MAAM;EACjB,OAAOpC,CAAC;AACV,CAAC;AAQD,MAAMqC,UAAU,gBAAG3B,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC1DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMW,SAAS,GAAGA,CAAeC,IAAY,EAAEC,KAAa,KAAqD;EAC/G,MAAMxC,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACW,UAAU,CAAC;EACnCrC,CAAC,CAACuC,IAAI,GAAGA,IAAI;EACbvC,CAAC,CAACwC,KAAK,GAAGA,KAAK;EACf,OAAOxC,CAAC;AACV,CAAC;AAQD,MAAMyC,WAAW,gBAAG/B,MAAM,CAACe,MAAM,cAACf,MAAM,CAACgB,MAAM,CAACP,UAAU,CAAC,EAAE;EAC3DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMe,UAAU,GAAGA,CAAeC,KAAa,EAAEC,KAAY,KAAqD;EAChH,MAAM5C,CAAC,GAAGU,MAAM,CAACgB,MAAM,CAACe,WAAW,CAAC;EACpCzC,CAAC,CAAC2C,KAAK,GAAGA,KAAK;EACf3C,CAAC,CAAC4C,KAAK,GAAGA,KAAK;EACf,OAAO5C,CAAC;AACV,CAAC;AASD;AACO,MAAM6C,IAAI,GACfC,OAIC,IACkD;EACnD,IAAI7C,CAAC,GAAG,CAAC;EACT,IAAI2C,KAAK,GAAGf,KAAK,EAAgB;EACjC,OAAO5B,CAAC,GAAG6C,OAAO,CAACC,QAAQ,CAACC,MAAM,IAAI/C,CAAC,GAAG6C,OAAO,CAACG,QAAQ,CAACD,MAAM,EAAE;IACjE,MAAME,UAAU,GAAGJ,OAAO,CAACC,QAAQ,CAAC9C,CAAC,CAAE;IACvC,MAAMkD,UAAU,GAAGL,OAAO,CAACG,QAAQ,CAAChD,CAAC,CAAE;IACvC,MAAMmD,UAAU,GAAGN,OAAO,CAACO,MAAM,CAACR,IAAI,CAACK,UAAU,EAAEC,UAAU,CAAC;IAC9D,IAAI,CAAC5D,KAAK,CAAC+D,MAAM,CAACF,UAAU,EAAEN,OAAO,CAACO,MAAM,CAACxB,KAAK,CAAC,EAAE;MACnDe,KAAK,GAAGW,OAAO,CAACX,KAAK,EAAEF,UAAU,CAACzC,CAAC,EAAEmD,UAAU,CAAC,CAAC;IACnD;IACAnD,CAAC,GAAGA,CAAC,GAAG,CAAC;EACX;EACA,IAAIA,CAAC,GAAG6C,OAAO,CAACC,QAAQ,CAACC,MAAM,EAAE;IAC/BJ,KAAK,GAAGW,OAAO,CAACX,KAAK,EAAEN,SAAS,CAAC,CAAC,EAAErC,CAAC,CAAC,CAAC;EACzC;EACA,IAAIA,CAAC,GAAG6C,OAAO,CAACG,QAAQ,CAACD,MAAM,EAAE;IAC/BJ,KAAK,GAAGW,OAAO,CAACX,KAAK,EAAET,UAAU,CAAC/C,GAAG,CAACoE,IAAI,CAACvD,CAAC,CAAC,CAAC6C,OAAO,CAACG,QAAQ,CAAC,CAAC,CAAC;EACnE;EACA,OAAOL,KAAK;AACd,CAAC;AAED;AAAA9B,OAAA,CAAA+B,IAAA,GAAAA,IAAA;AACO,MAAMU,OAAO,GAAAzC,OAAA,CAAAyC,OAAA,gBAAG/D,IAAI,CAACiE,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAK5B,WAAW,CAAC2B,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACO,MAAMf,KAAK,GAAA9B,OAAA,CAAA8B,KAAA,gBAAGpD,IAAI,CAACiE,IAAI,CAU5B,CAAC,EAAE,CACHC,IAAqD,EACrDX,QAA8B,EAC9BM,MAAmC,KACjC;EACF,IAAKK,IAAoB,CAAC/B,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOoB,QAAQ;EACjB;EACA,IAAIa,aAAa,GAAGb,QAAQ,CAACc,KAAK,EAAE;EACpC,IAAIC,OAAO,GAA2D1E,GAAG,CAAC2E,EAAE,CAACL,IAAI,CAAC;EAClF,OAAOtE,GAAG,CAAC4E,eAAe,CAACF,OAAO,CAAC,EAAE;IACnC,MAAMG,IAAI,GAAgB7E,GAAG,CAAC8E,YAAY,CAACJ,OAAO,CAAgB;IAClE,MAAMK,IAAI,GAAG/E,GAAG,CAACgF,YAAY,CAACN,OAAO,CAAC;IACtC,QAAQG,IAAI,CAACtC,IAAI;MACf,KAAK,OAAO;QAAE;UACZmC,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdA,IAAI,CAACE,OAAO,CAACJ,IAAI,CAACjC,KAAK,EAAEiC,IAAI,CAAChC,MAAM,CAAC;UACrC6B,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACb,KAAK,MAAMG,KAAK,IAAIL,IAAI,CAAC7B,MAAM,EAAE;YAC/BwB,aAAa,CAACW,IAAI,CAACD,KAAK,CAAC;UAC3B;UACAR,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,OAAO;QAAE;UACZP,aAAa,GAAGA,aAAa,CAACC,KAAK,CAACI,IAAI,CAAC1B,IAAI,EAAE0B,IAAI,CAACzB,KAAK,CAAC;UAC1DsB,OAAO,GAAGK,IAAI;UACd;QACF;MACA,KAAK,QAAQ;QAAE;UACbP,aAAa,CAACK,IAAI,CAACtB,KAAK,CAAC,GAAGU,MAAM,CAACT,KAAK,CAACqB,IAAI,CAACrB,KAAK,EAAEgB,aAAa,CAACK,IAAI,CAACtB,KAAK,CAAE,CAAC;UAChFmB,OAAO,GAAGK,IAAI;UACd;QACF;IACF;EACF;EACA,OAAOP,aAAa;AACtB,CAAC,CAAC", "ignoreList": []}