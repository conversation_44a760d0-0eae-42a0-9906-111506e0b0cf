"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUserRepository = void 0;
const createUserRepository = (prisma) => {
    return {
        async findById(id) {
            const user = await prisma.user.findUnique({
                where: { id },
            });
            if (!user)
                return null;
            return {
                id: user.id,
                email: user.email,
                firstName: user.firstName || undefined,
                lastName: user.lastName || undefined,
                role: user.role,
                isActive: user.isActive,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            };
        },
        async findByEmail(email) {
            const user = await prisma.user.findUnique({
                where: { email },
            });
            if (!user)
                return null;
            return {
                id: user.id,
                email: user.email,
                firstName: user.firstName || undefined,
                lastName: user.lastName || undefined,
                role: user.role,
                isActive: user.isActive,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            };
        },
        async findMany(skip = 0, take = 50) {
            const users = await prisma.user.findMany({
                skip,
                take,
                orderBy: { createdAt: 'desc' },
            });
            return users.map(user => ({
                id: user.id,
                email: user.email,
                firstName: user.firstName || undefined,
                lastName: user.lastName || undefined,
                role: user.role,
                isActive: user.isActive,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            }));
        },
        async create(input) {
            const user = await prisma.user.create({
                data: {
                    email: input.email,
                    password: input.password,
                    firstName: input.firstName,
                    lastName: input.lastName,
                    role: input.role || 'USER',
                },
            });
            return {
                id: user.id,
                email: user.email,
                firstName: user.firstName || undefined,
                lastName: user.lastName || undefined,
                role: user.role,
                isActive: user.isActive,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            };
        },
        async update(id, input) {
            const user = await prisma.user.update({
                where: { id },
                data: input,
            });
            return {
                id: user.id,
                email: user.email,
                firstName: user.firstName || undefined,
                lastName: user.lastName || undefined,
                role: user.role,
                isActive: user.isActive,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt,
            };
        },
        async delete(id) {
            await prisma.user.delete({
                where: { id },
            });
        },
        async count() {
            return prisma.user.count();
        },
    };
};
exports.createUserRepository = createUserRepository;
//# sourceMappingURL=user.repository.js.map