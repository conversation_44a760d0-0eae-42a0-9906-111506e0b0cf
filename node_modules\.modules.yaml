hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/aix-ppc64@0.25.9':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.9':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.9':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.9':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.9':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.9':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.9':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.9':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.9':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.9':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.9':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.9':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.9':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.9':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.9':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.9':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.9':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.9':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.9':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.9':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.9':
    '@esbuild/openbsd-x64': private
  '@esbuild/openharmony-arm64@0.25.9':
    '@esbuild/openharmony-arm64': private
  '@esbuild/sunos-x64@0.25.9':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.9':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.9':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.9':
    '@esbuild/win32-x64': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/remapping@2.3.5':
    '@jridgewell/remapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@rollup/rollup-android-arm-eabi@4.49.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.49.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.49.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.49.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.49.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.49.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.49.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.49.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.49.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.49.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.49.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-ppc64-gnu@4.49.0':
    '@rollup/rollup-linux-ppc64-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.49.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.49.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.49.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.49.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.49.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.49.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.49.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.49.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@tailwindcss/node@4.1.12':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.12':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.12':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.12':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.12':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.12':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.12':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.12':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.12':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.12':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.12':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.12':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.12':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.12':
    '@tailwindcss/oxide': private
  '@types/estree@1.0.8':
    '@types/estree': private
  chownr@3.0.0:
    chownr: private
  detect-libc@2.0.4:
    detect-libc: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  esbuild@0.25.9:
    esbuild: private
  fdir@6.5.0(picomatch@4.0.3):
    fdir: private
  fsevents@2.3.3:
    fsevents: private
  graceful-fs@4.2.11:
    graceful-fs: private
  jiti@2.5.1:
    jiti: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  magic-string@0.30.18:
    magic-string: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@3.0.1:
    mkdirp: private
  nanoid@3.3.11:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  rollup@4.49.0:
    rollup: private
  source-map-js@1.2.1:
    source-map-js: private
  tapable@2.2.3:
    tapable: private
  tar@7.4.3:
    tar: private
  tinyglobby@0.2.14:
    tinyglobby: private
  vite@7.1.3(jiti@2.5.1)(lightningcss@1.30.1):
    vite: private
  yallist@5.0.0:
    yallist: private
ignoredBuilds:
  - '@tailwindcss/oxide'
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Sat, 30 Aug 2025 15:17:11 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\edu\node_modules\.pnpm
virtualStoreDirMaxLength: 60
