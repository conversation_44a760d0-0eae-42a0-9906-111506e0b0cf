{"name": "edu", "version": "1.0.0", "description": "Educational platform with modular backend architecture", "main": "index.js", "scripts": {"dev": "pnpm --filter backend dev", "build": "pnpm --filter backend build", "start": "pnpm --filter backend start", "test": "pnpm --filter backend test", "prisma:merge": "pnpm --filter backend prisma:merge", "prisma:generate": "pnpm --filter backend prisma:generate", "prisma:migrate": "pnpm --filter backend prisma:migrate", "prisma:studio": "pnpm --filter backend prisma:studio"}, "keywords": ["education", "fastify", "graphql", "typescript"], "author": "", "license": "ISC", "packageManager": "pnpm@10.14.0", "workspaces": ["backend", "frontend"], "dependencies": {"@tailwindcss/vite": "^4.1.12", "tailwindcss": "^4.1.12"}}