{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../../src/shared/kernel/errors.ts"], "names": [], "mappings": ";;;AAiFA,kCAgCC;AAjHD,IAAY,SAuBX;AAvBD,WAAY,SAAS;IAEnB,gDAAmC,CAAA;IACnC,0CAA6B,CAAA;IAC7B,wDAA2C,CAAA;IAC3C,4CAA+B,CAAA;IAG/B,kDAAqC,CAAA;IACrC,4CAA+B,CAAA;IAC/B,8DAAiD,CAAA;IAGjD,sDAAyC,CAAA;IACzC,gEAAmD,CAAA;IACnD,4DAA+C,CAAA;IAC/C,gEAAmD,CAAA;IAGnD,4DAA+C,CAAA;IAC/C,8CAAiC,CAAA;IACjC,8DAAiD,CAAA;IACjD,wDAA2C,CAAA;AAC7C,CAAC,EAvBW,SAAS,yBAAT,SAAS,QAuBpB;AAED,MAAa,QAAS,SAAQ,KAAK;IAEf;IAEA;IACA;IAJlB,YACkB,IAAe,EAC/B,OAAe,EACC,aAAqB,GAAG,EACxB,OAAiC;QAEjD,KAAK,CAAC,OAAO,CAAC,CAAC;QALC,SAAI,GAAJ,IAAI,CAAW;QAEf,eAAU,GAAV,UAAU,CAAc;QACxB,YAAO,GAAP,OAAO,CAA0B;QAGjD,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QACvB,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,OAAO,GAAG,yBAAyB;QACxD,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,eAAe,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAC/D,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAO,GAAG,0BAA0B;QACtD,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAC5D,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,OAAO,GAAG,qBAAqB;QACvD,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,mBAAmB,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IACnE,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,QAAgB,EAAE,EAAW;QAC3C,MAAM,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,GAAG,QAAQ,YAAY,CAAC;QACrF,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,kBAAkB,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IAClE,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,QAAgB,EAAE,KAAc;QACnD,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,cAAc,KAAK,iBAAiB,CAAC,CAAC,CAAC,GAAG,QAAQ,iBAAiB,CAAC;QACvG,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAAe,EAAE,OAAiC;QAClE,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,gBAAgB,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;IACzE,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAe;QACjC,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,uBAAuB,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,OAAO,GAAG,uBAAuB;QAC/C,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,qBAAqB,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;CACF;AA7CD,4BA6CC;AAWD,SAAgB,WAAW,CAAC,KAAY,EAAE,IAAa;IACrD,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;QAC9B,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI;SACL,CAAC;IACJ,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,+BAA+B,EAAE,CAAC;QACnD,MAAM,WAAW,GAAG,KAAY,CAAC;QACjC,QAAQ,WAAW,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,OAAO;gBACV,OAAO,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC,CAAC;YAC/E,KAAK,OAAO;gBACV,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;YAC1D;gBACE,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,2BAA2B,CAAC,EAAE,IAAI,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;QAC9B,OAAO,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB,EAAE,EAAE,MAAM,EAAG,KAAa,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACxG,CAAC;IAGD,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,8BAA8B,CAAC,EAAE,IAAI,CAAC,CAAC;AAC/F,CAAC"}