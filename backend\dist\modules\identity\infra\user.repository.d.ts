import { PrismaClient } from '../../../prisma/generated';
import { CreateUserInput, UpdateUserInput, User } from '../domain/types';
export interface UserRepository {
    findById(id: string): Promise<User | null>;
    findByEmail(email: string): Promise<User | null>;
    findMany(skip?: number, take?: number): Promise<User[]>;
    create(input: CreateUserInput & {
        password: string;
    }): Promise<User>;
    update(id: string, input: UpdateUserInput): Promise<User>;
    delete(id: string): Promise<void>;
    count(): Promise<number>;
}
export declare const createUserRepository: (prisma: PrismaClient) => UserRepository;
//# sourceMappingURL=user.repository.d.ts.map