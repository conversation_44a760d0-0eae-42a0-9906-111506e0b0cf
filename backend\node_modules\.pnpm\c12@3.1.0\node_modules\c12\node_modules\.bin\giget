#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/giget@2.0.0/node_modules/giget/dist/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/giget@2.0.0/node_modules/giget/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/giget@2.0.0/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/giget@2.0.0/node_modules/giget/dist/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/giget@2.0.0/node_modules/giget/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/giget@2.0.0/node_modules:/mnt/c/Users/<USER>/Desktop/edu/backend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../giget@2.0.0/node_modules/giget/dist/cli.mjs" "$@"
else
  exec node  "$basedir/../../../../../giget@2.0.0/node_modules/giget/dist/cli.mjs" "$@"
fi
