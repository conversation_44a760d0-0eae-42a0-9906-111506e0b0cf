import { PrismaClient } from '../../../prisma/generated';
import { AppContext } from '../../../src/app';
export declare const getTypeDefs: () => string;
export declare const getResolvers: (prisma: PrismaClient) => {
    Query: {
        me: (_: any, __: any, context: AppContext) => Promise<import("../domain/types").User | null>;
        users: (_: any, args: {
            skip?: number;
            take?: number;
        }, context: AppContext) => Promise<import("../domain/types").User[]>;
        user: (_: any, args: {
            id: string;
        }, context: AppContext) => Promise<import("../domain/types").User>;
    };
    Mutation: {
        register: (_: any, args: {
            input: any;
        }) => Promise<import("../domain/types").AuthPayload>;
        login: (_: any, args: {
            input: any;
        }) => Promise<import("../domain/types").AuthPayload>;
        refreshToken: (_: any, __: any, context: AppContext) => Promise<string>;
        updateUser: (_: any, args: {
            id: string;
            input: any;
        }, context: AppContext) => Promise<import("../domain/types").User>;
        deleteUser: (_: any, args: {
            id: string;
        }, context: AppContext) => Promise<boolean>;
    };
};
//# sourceMappingURL=index.d.ts.map