import { readdirSync, existsSync } from 'fs';
import { join } from 'path';
import { PrismaClient } from '../../../prisma/generated';
import { logger } from './logger';

export interface ModuleGraphQL {
  getTypeDefs: () => string;
  getResolvers: (prisma: PrismaClient) => Record<string, any>;
}

export interface LoadedModules {
  typeDefs: string;
  resolvers: Record<string, any>;
}

const baseTypeDefs = `
  scalar DateTime
  scalar JSON

  type Query {
    _empty: String
  }

  type Mutation {
    _empty: String
  }

  type Subscription {
    _empty: String
  }
`;

export async function loadModules(prisma: PrismaClient): Promise<LoadedModules> {
  const modulesDir = join(__dirname, '../../../modules');
  
  if (!existsSync(modulesDir)) {
    logger.info('No modules directory found, using base schema only');
    return {
      typeDefs: baseTypeDefs,
      resolvers: {
        Query: {
          _empty: () => 'GraphQL server is running',
        },
        Mutation: {
          _empty: () => 'GraphQL server is running',
        },
      },
    };
  }

  const moduleNames = readdirSync(modulesDir, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name);

  const allTypeDefs: string[] = [baseTypeDefs];
  const allResolvers: Record<string, any> = {
    Query: {
      _empty: () => 'GraphQL server is running',
    },
    Mutation: {
      _empty: () => 'GraphQL server is running',
    },
  };

  for (const moduleName of moduleNames) {
    try {
      const graphqlPath = join(modulesDir, moduleName, 'graphql', 'index.ts');
      
      if (existsSync(graphqlPath)) {
        logger.info(`Loading GraphQL module: ${moduleName}`);
        
        // Dynamic import of the module
        const moduleGraphQL: ModuleGraphQL = await import(graphqlPath);
        
        if (typeof moduleGraphQL.getTypeDefs === 'function') {
          const typeDefs = moduleGraphQL.getTypeDefs();
          allTypeDefs.push(typeDefs);
        }
        
        if (typeof moduleGraphQL.getResolvers === 'function') {
          const resolvers = moduleGraphQL.getResolvers(prisma);
          
          // Merge resolvers
          Object.keys(resolvers).forEach(type => {
            if (!allResolvers[type]) {
              allResolvers[type] = {};
            }
            Object.assign(allResolvers[type], resolvers[type]);
          });
        }
        
        logger.info(`✅ Successfully loaded module: ${moduleName}`);
      } else {
        logger.debug(`No GraphQL schema found for module: ${moduleName}`);
      }
    } catch (error) {
      logger.error({ error, moduleName }, `❌ Failed to load module ${moduleName}`);
      // Continue loading other modules even if one fails
    }
  }

  const combinedTypeDefs = allTypeDefs.join('\n\n');
  
  logger.info(`🚀 Loaded ${moduleNames.length} modules with GraphQL schemas`);
  
  return {
    typeDefs: combinedTypeDefs,
    resolvers: allResolvers,
  };
}

export function createModuleRepository<T>(
  repositoryFactory: (prisma: PrismaClient) => T,
  prisma: PrismaClient
): T {
  return repositoryFactory(prisma);
}
