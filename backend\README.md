# Educational Platform Backend

A modular Fastify v5 backend with TypeScript, Mercurius GraphQL, and Prisma ORM.

## Features

- **Fastify v5** - High-performance web framework
- **Mercurius** - GraphQL server for Fastify (not Apollo)
- **TypeScript** - Full type safety
- **Prisma ORM** - Database toolkit with PostgreSQL
- **Modular Architecture** - Each feature in its own module
- **JWT Authentication** - Secure user authentication
- **Error Handling** - Centralized error management
- **Logging** - Structured logging with Pino
- **Security** - CORS, Helmet, Rate limiting

## Project Structure

```
backend/
├── src/
│   ├── app.ts              # Main Fastify application
│   ├── index.ts            # Application entry point
│   ├── shared/
│   │   └── kernel/         # Shared utilities
│   └── types/              # Global type definitions
├── modules/
│   └── identity/           # User authentication module
│       ├── application/    # Business logic
│       ├── domain/         # Types and interfaces
│       ├── graphql/        # GraphQL schema and resolvers
│       ├── infra/          # Database repositories
│       └── prisma/         # Module-specific Prisma schema
├── prisma/
│   ├── schema.prisma       # Merged Prisma schema
│   └── generated/          # Generated Prisma client
└── scripts/
    └── merge-schemas.ts    # Schema merging utility
```

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- pnpm package manager

### Installation

1. Install dependencies:
```bash
pnpm install
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your database URL and other settings
```

3. Generate Prisma client:
```bash
pnpm prisma:merge
pnpm prisma:generate
```

4. Run database migrations:
```bash
pnpm prisma:migrate dev --name init
```

### Development

Start the development server:
```bash
pnpm dev
```

The server will start at `http://localhost:4000`
- GraphQL Playground: `http://localhost:4000/graphiql`
- Health check: `http://localhost:4000/health`

### Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm prisma:merge` - Merge module schemas
- `pnpm prisma:generate` - Generate Prisma client
- `pnpm prisma:migrate` - Run database migrations
- `pnpm prisma:studio` - Open Prisma Studio

## Adding New Modules

1. Create module directory: `modules/<module-name>/`
2. Add Prisma schema: `modules/<module-name>/prisma/schema.prisma`
3. Define domain types: `modules/<module-name>/domain/types.ts`
4. Create repository: `modules/<module-name>/infra/<module-name>.repository.ts`
5. Add business logic: `modules/<module-name>/application/<module-name>.service.ts`
6. Create GraphQL schema: `modules/<module-name>/graphql/index.ts`
7. Run `pnpm prisma:merge` and `pnpm prisma:generate`

## GraphQL API

### Authentication

```graphql
mutation Register {
  register(input: {
    email: "<EMAIL>"
    password: "password123"
    firstName: "John"
    lastName: "Doe"
  }) {
    token
    user {
      id
      email
      firstName
      lastName
    }
  }
}

mutation Login {
  login(input: {
    email: "<EMAIL>"
    password: "password123"
  }) {
    token
    user {
      id
      email
      role
    }
  }
}
```

### User Management

```graphql
query Me {
  me {
    id
    email
    firstName
    lastName
    role
  }
}

query Users {
  users {
    id
    email
    firstName
    lastName
    role
    isActive
  }
}
```

## Environment Variables

See `.env.example` for all available environment variables.

## License

ISC
