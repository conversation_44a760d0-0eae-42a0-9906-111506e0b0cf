{"version": 3, "file": "channelState.js", "names": ["Effect", "_interopRequireWildcard", "require", "_Predicate", "OpCodes", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ChannelStateTypeId", "exports", "Symbol", "for", "channelStateVariance", "_E", "_", "_R", "proto", "Done", "op", "create", "_tag", "OP_DONE", "Emit", "OP_EMIT", "fromEffect", "effect", "OP_FROM_EFFECT", "Read", "upstream", "onEffect", "onEmit", "onDone", "OP_READ", "isChannelState", "u", "hasProperty", "isDone", "self", "isEmit", "isFromEffect", "isRead", "void", "effectOrUndefinedIgnored", "ignore", "undefined"], "sources": ["../../../../src/internal/channel/channelState.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAqD,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAGrD;AACO,MAAMkB,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,gBAAGE,MAAM,CAACC,GAAG,CAAC,qBAAqB,CAAC;AAkBnE,MAAMC,oBAAoB,GAAG;EAC3B;EACAC,EAAE,EAAGC,CAAQ,IAAKA,CAAC;EACnB;EACAC,EAAE,EAAGD,CAAQ,IAAKA;CACnB;AAED;AACA,MAAME,KAAK,GAAG;EACZ,CAACR,kBAAkB,GAAGI;CACvB;AAqCD;AACO,MAAMK,IAAI,GAAGA,CAAA,KAAiC;EACnD,MAAMC,EAAE,GAAGb,MAAM,CAACc,MAAM,CAACH,KAAK,CAAC;EAC/BE,EAAE,CAACE,IAAI,GAAGhC,OAAO,CAACiC,OAAO;EACzB,OAAOH,EAAE;AACX,CAAC;AAED;AAAAT,OAAA,CAAAQ,IAAA,GAAAA,IAAA;AACO,MAAMK,IAAI,GAAGA,CAAA,KAAiC;EACnD,MAAMJ,EAAE,GAAGb,MAAM,CAACc,MAAM,CAACH,KAAK,CAAC;EAC/BE,EAAE,CAACE,IAAI,GAAGhC,OAAO,CAACmC,OAAO;EACzB,OAAOL,EAAE;AACX,CAAC;AAED;AAAAT,OAAA,CAAAa,IAAA,GAAAA,IAAA;AACO,MAAME,UAAU,GAAaC,MAA8B,IAAwB;EACxF,MAAMP,EAAE,GAAGb,MAAM,CAACc,MAAM,CAACH,KAAK,CAAC;EAC/BE,EAAE,CAACE,IAAI,GAAGhC,OAAO,CAACsC,cAAc;EAChCR,EAAE,CAACO,MAAM,GAAGA,MAAM;EAClB,OAAOP,EAAE;AACX,CAAC;AAED;AAAAT,OAAA,CAAAe,UAAA,GAAAA,UAAA;AACO,MAAMG,IAAI,GAAGA,CAClBC,QAA2B,EAC3BC,QAAkF,EAClFC,MAAqE,EACrEC,MAAwF,KAC9D;EAC1B,MAAMb,EAAE,GAAGb,MAAM,CAACc,MAAM,CAACH,KAAK,CAAC;EAC/BE,EAAE,CAACE,IAAI,GAAGhC,OAAO,CAAC4C,OAAO;EACzBd,EAAE,CAACU,QAAQ,GAAGA,QAAQ;EACtBV,EAAE,CAACW,QAAQ,GAAGA,QAAQ;EACtBX,EAAE,CAACY,MAAM,GAAGA,MAAM;EAClBZ,EAAE,CAACa,MAAM,GAAGA,MAAM;EAClB,OAAOb,EAAE;AACX,CAAC;AAED;AAAAT,OAAA,CAAAkB,IAAA,GAAAA,IAAA;AACO,MAAMM,cAAc,GAAIC,CAAU,IAA0C,IAAAC,sBAAW,EAACD,CAAC,EAAE1B,kBAAkB,CAAC;AAErH;AAAAC,OAAA,CAAAwB,cAAA,GAAAA,cAAA;AACO,MAAMG,MAAM,GAAUC,IAAwB,IAAoBA,IAAkB,CAACjB,IAAI,KAAKhC,OAAO,CAACiC,OAAO;AAEpH;AAAAZ,OAAA,CAAA2B,MAAA,GAAAA,MAAA;AACO,MAAME,MAAM,GAAUD,IAAwB,IAAoBA,IAAkB,CAACjB,IAAI,KAAKhC,OAAO,CAACmC,OAAO;AAEpH;AAAAd,OAAA,CAAA6B,MAAA,GAAAA,MAAA;AACO,MAAMC,YAAY,GAAUF,IAAwB,IACxDA,IAAkB,CAACjB,IAAI,KAAKhC,OAAO,CAACsC,cAAc;AAErD;AAAAjB,OAAA,CAAA8B,YAAA,GAAAA,YAAA;AACO,MAAMC,MAAM,GAAUH,IAAwB,IAAoBA,IAAkB,CAACjB,IAAI,KAAKhC,OAAO,CAAC4C,OAAO;AAEpH;AAAAvB,OAAA,CAAA+B,MAAA,GAAAA,MAAA;AACO,MAAMf,MAAM,GAAUY,IAAwB,IACnDE,YAAY,CAACF,IAAI,CAAC,GAAGA,IAAI,CAACZ,MAAmC,GAAGzC,MAAM,CAACyD,IAAI;AAE7E;AAAAhC,OAAA,CAAAgB,MAAA,GAAAA,MAAA;AACO,MAAMiB,wBAAwB,GAAUL,IAAwB,IACrEE,YAAY,CAACF,IAAI,CAAC,GAAGrD,MAAM,CAAC2D,MAAM,CAACN,IAAI,CAACZ,MAAmC,CAAC,GAAGmB,SAAS;AAAAnC,OAAA,CAAAiC,wBAAA,GAAAA,wBAAA", "ignoreList": []}