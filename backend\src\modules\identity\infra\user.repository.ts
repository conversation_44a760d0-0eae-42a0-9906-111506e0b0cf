import { PrismaClient } from '../../../../prisma/generated';
import { CreateUserInput, UpdateUserInput, User } from '../domain/types';

export interface UserRepository {
  findById(id: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findMany(skip?: number, take?: number): Promise<User[]>;
  create(input: CreateUserInput & { password: string }): Promise<User>;
  update(id: string, input: UpdateUserInput): Promise<User>;
  delete(id: string): Promise<void>;
  count(): Promise<number>;
}

export const createUserRepository = (prisma: PrismaClient): UserRepository => {
  return {
    async findById(id: string): Promise<User | null> {
      const user = await prisma.user.findUnique({
        where: { id },
      });
      
      if (!user) return null;
      
      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined,
        role: user.role as any,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    },

    async findByEmail(email: string): Promise<User | null> {
      const user = await prisma.user.findUnique({
        where: { email },
      });
      
      if (!user) return null;
      
      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined,
        role: user.role as any,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    },

    async findMany(skip = 0, take = 50): Promise<User[]> {
      const users = await prisma.user.findMany({
        skip,
        take,
        orderBy: { createdAt: 'desc' },
      });
      
      return users.map(user => ({
        id: user.id,
        email: user.email,
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined,
        role: user.role as any,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      }));
    },

    async create(input: CreateUserInput & { password: string }): Promise<User> {
      const user = await prisma.user.create({
        data: {
          email: input.email,
          password: input.password,
          firstName: input.firstName,
          lastName: input.lastName,
          role: input.role || 'USER',
        },
      });
      
      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined,
        role: user.role as any,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    },

    async update(id: string, input: UpdateUserInput): Promise<User> {
      const user = await prisma.user.update({
        where: { id },
        data: input,
      });
      
      return {
        id: user.id,
        email: user.email,
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined,
        role: user.role as any,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };
    },

    async delete(id: string): Promise<void> {
      await prisma.user.delete({
        where: { id },
      });
    },

    async count(): Promise<number> {
      return prisma.user.count();
    },
  };
};
