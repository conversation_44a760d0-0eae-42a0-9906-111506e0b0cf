export declare enum ErrorCode {
    UNAUTHENTICATED = "UNAUTHENTICATED",
    UNAUTHORIZED = "UNAUTHORIZED",
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS",
    TOKEN_EXPIRED = "TOKEN_EXPIRED",
    VALIDATION_ERROR = "VALIDATION_ERROR",
    INVALID_INPUT = "INVALID_INPUT",
    REQUIRED_FIELD_MISSING = "REQUIRED_FIELD_MISSING",
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND",
    RESOURCE_ALREADY_EXISTS = "RESOURCE_ALREADY_EXISTS",
    OPERATION_NOT_ALLOWED = "OPERATION_NOT_ALLOWED",
    BUSINESS_RULE_VIOLATION = "BUSINESS_RULE_VIOLATION",
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
    DATABASE_ERROR = "DATABASE_ERROR",
    EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR",
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
}
export declare class AppError extends Error {
    readonly code: ErrorCode;
    readonly statusCode: number;
    readonly details?: Record<string, unknown> | undefined;
    constructor(code: ErrorCode, message: string, statusCode?: number, details?: Record<string, unknown> | undefined);
    static unauthenticated(message?: string): AppError;
    static unauthorized(message?: string): AppError;
    static invalidCredentials(message?: string): AppError;
    static notFound(resource: string, id?: string): AppError;
    static alreadyExists(resource: string, field?: string): AppError;
    static validation(message: string, details?: Record<string, unknown>): AppError;
    static businessRule(message: string): AppError;
    static internal(message?: string): AppError;
}
export interface ErrorResponse {
    code: ErrorCode;
    message: string;
    statusCode: number;
    details?: Record<string, unknown>;
    timestamp: string;
    path?: string;
}
export declare function formatError(error: Error, path?: string): ErrorResponse;
//# sourceMappingURL=errors.d.ts.map