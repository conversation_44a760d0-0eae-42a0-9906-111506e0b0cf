{"name": "backend", "version": "1.0.0", "description": "Fastify backend with modular GraphQL architecture", "main": "dist/index.js", "scripts": {"dev": "cross-env NODE_ENV=development tsx watch src/index.ts", "build": "tsc", "start": "cross-env NODE_ENV=production node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "prisma:merge": "tsx scripts/merge-schemas.ts", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "keywords": ["fastify", "graphql", "typescript", "prisma"], "author": "", "license": "ISC", "dependencies": {"@fastify/cors": "^10.0.1", "@fastify/helmet": "^12.0.1", "@fastify/jwt": "^9.0.1", "@fastify/rate-limit": "^10.1.1", "@prisma/client": "^6.15.0", "bcrypt": "^5.1.1", "fastify": "^5.5.0", "graphql": "^16.11.0", "jsonwebtoken": "^9.0.2", "mercurius": "^15.1.0", "pino": "^9.5.0", "pino-pretty": "^11.3.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.9.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "cross-env": "^7.0.3", "eslint": "^9.15.0", "prisma": "^6.15.0", "tsx": "^4.20.5", "typescript": "^5.9.2", "vitest": "^2.1.5"}}