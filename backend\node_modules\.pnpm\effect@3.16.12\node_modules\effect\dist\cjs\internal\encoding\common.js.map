{"version": 3, "file": "common.js", "names": ["_Predicate", "require", "DecodeExceptionTypeId", "exports", "Symbol", "for", "DecodeException", "input", "message", "out", "_tag", "isString", "isDecodeException", "u", "hasProperty", "EncodeExceptionTypeId", "EncodeException", "isEncodeException", "encoder", "TextEncoder", "decoder", "TextDecoder"], "sources": ["../../../../src/internal/encoding/common.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,UAAA,GAAAC,OAAA;AAGA;AACO,MAAMC,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,gBAAmCE,MAAM,CAACC,GAAG,CAC7E,+BAA+B,CACE;AAEnC;AACO,MAAMC,eAAe,GAAGA,CAACC,KAAa,EAAEC,OAAgB,KAA8B;EAC3F,MAAMC,GAAG,GAAsC;IAC7CC,IAAI,EAAE,iBAAiB;IACvB,CAACR,qBAAqB,GAAGA,qBAAqB;IAC9CK;GACD;EACD,IAAI,IAAAI,mBAAQ,EAACH,OAAO,CAAC,EAAE;IACrBC,GAAG,CAACD,OAAO,GAAGA,OAAO;EACvB;EACA,OAAOC,GAAG;AACZ,CAAC;AAED;AAAAN,OAAA,CAAAG,eAAA,GAAAA,eAAA;AACO,MAAMM,iBAAiB,GAAIC,CAAU,IAAoC,IAAAC,sBAAW,EAACD,CAAC,EAAEX,qBAAqB,CAAC;AAErH;AAAAC,OAAA,CAAAS,iBAAA,GAAAA,iBAAA;AACO,MAAMG,qBAAqB,GAAAZ,OAAA,CAAAY,qBAAA,gBAAmCX,MAAM,CAACC,GAAG,CAC7E,+BAA+B,CACE;AAEnC;AACO,MAAMW,eAAe,GAAGA,CAACT,KAAa,EAAEC,OAAgB,KAA8B;EAC3F,MAAMC,GAAG,GAAsC;IAC7CC,IAAI,EAAE,iBAAiB;IACvB,CAACK,qBAAqB,GAAGA,qBAAqB;IAC9CR;GACD;EACD,IAAI,IAAAI,mBAAQ,EAACH,OAAO,CAAC,EAAE;IACrBC,GAAG,CAACD,OAAO,GAAGA,OAAO;EACvB;EACA,OAAOC,GAAG;AACZ,CAAC;AAED;AAAAN,OAAA,CAAAa,eAAA,GAAAA,eAAA;AACO,MAAMC,iBAAiB,GAAIJ,CAAU,IAAoC,IAAAC,sBAAW,EAACD,CAAC,EAAEE,qBAAqB,CAAC;AAErH;AAAAZ,OAAA,CAAAc,iBAAA,GAAAA,iBAAA;AACO,MAAMC,OAAO,GAAAf,OAAA,CAAAe,OAAA,gBAAG,IAAIC,WAAW,EAAE;AAExC;AACO,MAAMC,OAAO,GAAAjB,OAAA,CAAAiB,OAAA,gBAAG,IAAIC,WAAW,EAAE", "ignoreList": []}