import { createApp } from './app';
import { logger } from './shared/kernel/logger';

async function start() {
  try {
    const app = await createApp();
    
    const port = parseInt(process.env.PORT || '4000');
    const host = process.env.HOST || '0.0.0.0';
    
    await app.listen({ port, host });
    
    logger.info(`🚀 Server ready at http://${host}:${port}`);
    logger.info(`📊 GraphQL Playground available at http://${host}:${port}/graphiql`);
    logger.info(`🏥 Health check available at http://${host}:${port}/health`);
    
  } catch (error) {
    logger.error({ error }, '❌ Failed to start server');
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error({ reason, promise }, 'Unhandled Rejection');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logger.error({ error }, 'Uncaught Exception');
  process.exit(1);
});

start().catch((error) => {
  logger.error('Failed to start application:', error);
  process.exit(1);
});
